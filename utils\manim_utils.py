import re
from typing import Tuple

class ManimUtils:
    """Manim工具类，提供Manim代码解析功能"""

    @staticmethod
    def extract_manim_code(text: str) -> str:
        """
        从文本中提取Manim代码
        
        Args:
            text: 包含Manim代码的文本
            
        Returns:
            str: 提取的Manim代码
        """
        # 尝试查找```html和```之间的内容
        code_pattern = r'```html\s*([\s\S]*?)\s*```'
        matches = re.findall(code_pattern, text)
        
        if matches:
            # 返回第一个匹配的内容
            return matches[0].strip()
        
        # 尝试查找```和```之间的内容（无语言标识）
        code_pattern = r'```\s*([\s\S]*?)\s*```'
        matches = re.findall(code_pattern, text)
        
        if matches:
            # 返回第一个匹配的内容
            return matches[0].strip()
        
        # 如果没有代码块标记，尝试查找包含from manim import的完整代码段
        if 'from manim import' in text:
            # 查找从from manim import开始到文本结尾的内容
            start_index = text.find('from manim import')
            if start_index != -1:
                return text[start_index:].strip()
        
        # 如果没有找到任何代码，返回原文本（让后续处理决定如何处理）
        return text.strip()

    @staticmethod
    def extract_class_name(manim_code: str) -> str:
        """
        从Manim代码中提取类名
        
        Args:
            manim_code: Manim代码字符串
            
        Returns:
            str: 类名，如果找不到则返回空字符串
        """
        # 查找类定义
        class_pattern = r'class\s+(\w+)\s*\([^)]*Scene[^)]*\)'
        matches = re.findall(class_pattern, manim_code)
        
        if matches:
            return matches[0]
        
        return ""
