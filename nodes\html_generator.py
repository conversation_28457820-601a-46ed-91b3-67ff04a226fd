import os
import uuid
import hashlib
import json
from typing import Dict, Any, List, Tuple
from langchain_core.messages import BaseMessage

from utils.file_utils import FileManager
from utils.prompt_utils import PromptManager
from utils.s3_utils import SVGSaveS3
from utils.db_utils import write_project_video_scene, write_project_video_scene_material
from utils.manim_utils import ManimUtils
from llm.claude_local import ClaudeLocalProvider
from llm.batch_processor import BatchProcessor
from utils.db_pool import DBConnectionPool

class HTMLGenerator:
    """HTML代码生成节点"""

    def __init__(self, conf_path="../conf/conf.ini"):
        """
        初始化HTML代码生成节点

        Args:
            conf_path: 配置文件路径
        """
        self.file_manager = FileManager()
        self.prompt_manager = PromptManager()
        self.llm = ClaudeLocalProvider()
        self.batch_processor = BatchProcessor(provider=self.llm, max_concurrent=10)
        self.conf_path = conf_path

        # 初始化S3存储工具
        self.s3_handler = SVGSaveS3(conf_path)

        # 初始化数据库连接池
        self.db_pool = DBConnectionPool(conf_path)

        # 创建HTML输出目录
        self.html_output_dir = os.path.join("data", "html_output")
        os.makedirs(self.html_output_dir, exist_ok=True)

        # 生成唯一的项目ID
        self.project_id = None

    async def process(self, state: Dict[str, Any]) -> Dict[str, Any]:
        """
        处理数据

        Args:
            state: 当前状态

        Returns:
            Dict[str, Any]: 更新后的状态
        """
        print("\n" + "="*50)
        print("【开始执行】HTML代码生成 (HTMLGenerator)")
        print("="*50)

        try:
            # 初始化项目信息和目录
            project_dir, project_hash = self._init_project_info(state)

            # 执行批处理生成HTML代码
            results = await self._generate_html_batch(state, project_hash, project_dir)

            # 保存脚本文件
            data_state_path = self._save_script_file(state, project_dir, project_hash)

            # 更新状态和保存结果
            scene_paths = self._update_state_with_results(state, results)

            # 添加项目路径到状态中，供后续节点使用
            state["data"]["project_dir"] = project_dir
            state["data"]["data_state_path"] = data_state_path
            state["data"]["scene_paths"] = scene_paths

            state["current_step"] = "generate_html"

            print("="*50)
            print("【完成执行】HTML代码生成 (HTMLGenerator)")
            print("="*50 + "\n")

            return state
        finally:
            # 这里不需要显式关闭连接池，因为连接会自动返回到池中
            pass

    def _init_project_info(self, state: Dict[str, Any]) -> Tuple[str, str]:
        """
        初始化项目信息和目录

        Args:
            state: 当前状态

        Returns:
            Tuple[str, str]: 项目目录路径和项目哈希
        """
        # 获取热词和标题信息
        trend_word = state["data"].get("trend_word", "未知热词")
        title = state["data"].get("news_report", {}).get("title", "未知标题")
        print(f"热词: {trend_word}, 标题: {title}")

        # 生成唯一的项目标识符
        project_hash = hashlib.md5(f"{trend_word}".encode()).hexdigest()
        project_dir = f"html_video/{project_hash}"
        print(f"项目目录: {project_dir}")

        # 创建项目目录
        os.makedirs(os.path.join(self.html_output_dir, project_hash), exist_ok=True)

        return project_dir, project_hash

    async def _generate_html_batch(self, state: Dict[str, Any], project_hash: str, project_dir: str) -> List[Tuple[str, str, str]]:
        """
        执行批处理生成HTML代码

        Args:
            state: 当前状态
            project_hash: 项目哈希
            project_dir: 项目目录

        Returns:
            List[Tuple[str, str, str]]: 生成结果列表，每项包含(场景ID, HTML代码, S3路径)
        """
        # 从news_report中获取SVG提示词（现在用于HTML代码生成）
        news_report = state["data"].get("news_report", {})
        sections = news_report.get("sections", [])

        # 准备批处理数据
        batch_items = []

        # 收集所有需要处理的场景和镜头
        for i, section in enumerate(sections):
            section_id = f"section_{i+1}"

            # 获取当前场景的子镜头列表
            subsections = section.get("subsections", [])

            if subsections:
                # 如果有子镜头，则处理每个子镜头的svg_prompt
                for j, subsection in enumerate(subsections):
                    shot_id = f"{section_id}_shot_{j+1}"
                    svg_prompt = subsection.get("svg_prompt")
                    if svg_prompt:
                        batch_items.append((shot_id, svg_prompt))
                        print(f"准备处理场景 {section_id} 的镜头 {j+1} 的HTML代码生成")
            else:
                # 如果没有子镜头，则处理整个场景的svg_prompt
                svg_prompt = section.get("svg_prompt")
                if svg_prompt:
                    batch_items.append((section_id, svg_prompt))
                    print(f"准备处理场景 {i+1} 的HTML代码生成")

        print(f"准备批处理 {len(batch_items)} 个场景的HTML代码生成")

        # 定义一个异步的包装函数来处理响应
        async def process_html_response_wrapper(item, response):
            return await self._process_html_response(item, response, project_hash, project_dir)

        # 执行批处理（启用深度思考，使用temperature=0.0以获得确定性输出）
        results = await self.batch_processor.process_batch(
            items=batch_items,
            create_messages_func=self._create_messages_for_html,
            process_response_func=process_html_response_wrapper,
            max_retries=5,
            temperature=0.0
        )

        return results

    def _create_messages_for_html(self, item: Tuple[str, str]) -> List[BaseMessage]:
        """
        创建HTML代码生成的消息

        Args:
            item: 包含场景ID和提示词的元组

        Returns:
            List[BaseMessage]: 消息列表
        """
        scene_id, animation_prompt = item
        print(f"准备处理场景: {scene_id}")
        return self.llm.create_messages(
            system_prompt="""

# ROLE: HTML电影级动画生成器 & 顶级前端动画艺术家

# MISSION:
Based **primarily and strictly** on the provided **《HTML动画设计文档》** (which will follow this prompt), you are to generate a single, self-contained HTML file. This file will meticulously implement the animation described. Your output must be **production-ready code**, demonstrating exceptional precision in timing and visual fidelity to the design document, suitable for direct use and screen recording.

# GUIDING PRINCIPLES:
1.  **DOCUMENT FIDELITY (ABSOLUTE PRIORITY):** Every specification within the 《HTML动画设计文档》 (timings, element IDs, positions, sizes, assets, text content, animations, element states per timeslice) is **non-negotiable and must be implemented exactly as described.**
2.  **CINEMATIC QUALITY:** Where the document allows for interpretation (e.g., unspecified transition details not explicitly defined), strive for a "movie-grade" feel: 极致的流畅度、美学表现和情绪共鸣, ensuring these enhancements do not contradict any specified detail.
3.  **MODERN & IMMERSIVE NARRATIVE:** Employ clean, impactful visuals and elegant information presentation as guided by the design document.

# TECHNICAL SPECIFICATIONS (STRICT ADHERENCE REQUIRED):

## 1. HTML Structure & Canvas:
*   **Single HTML File:** All CSS within `<style>` tags, all JavaScript within `<script>` tags (preferably at the end of `<body>` or with `defer`).
*   **Animation Container:** The main animation container (e.g., `<div class="animation-container">`) must be `100vw` wide and `100vh` high.
*   **16:9 Aspect Ratio & Responsiveness:**
    *   All visual elements within the main container must collectively maintain a strict 16:9 aspect ratio as per the design document's logical grid.
    *   **ALL positioning and sizing of elements MUST use `vw` and `vh` units (or CSS calc involving them) derived from the design document's 14x8 unit grid.** This ensures true responsiveness while preserving the 16:9 composition.
*   **HTML Element Generation:**
    *   All visual elements (images, text blocks) described in section `3.1`, `3.2`, and `3.4` of the 《HTML动画设计文档》 **must be pre-defined in the HTML structure within the animation container.**
    *   Each element **must have the `元素ID`** specified in the design document as its HTML `id` attribute.
    *   Elements should initially be styled as hidden (e.g., `opacity: 0; pointer-events: none;` or `display: none;` which can be toggled by a class) unless they appear in the very first timeslice.
*   **Compatibility:** Optimized for modern browsers (Chrome, Firefox, Safari, Edge).

## 2. 《HTML动画设计文档》 PARSING & COORDINATE/STYLE IMPLEMENTATION (CRITICAL PRECISION REQUIRED):

**2.1. Global Units & Coordinate Mapping:**
*   The Design Document uses a **14 (width) x 8 (height) unit grid** for a 1920x1080px (16:9) canvas, with **(0,0) as the CENTER point.**
*   **Base Unit Calculation (Mandatory for CSS):**
    ```javascript
    // These are for internal calculation reference if needed by JS,
    // but primary use is for direct CSS vw/vh calculations.
    const base_unit_vw = 100 / 14; // Approx 7.1428vw per design document width unit
    const base_unit_vh = 100 / 8;  // 12.5vh per design document height unit
    ```
*   **Element Sizing (Mandatory CSS):**
    *   `width: [Design_Doc_Width_Units * (100/14)]vw;`
    *   `height: [Design_Doc_Height_Units * (100/8)]vh;`
*   **Element Positioning (Mandatory CSS - Use `transform` for centering from a 50%/50% origin):**
    *   All elements to be positioned using `position: absolute; left: 50%; top: 50%; transform-origin: center center;`
    *   Their final visual position is achieved via:
        `transform: translate(calc(-50% + var(--x-offset)), calc(-50% + var(--y-offset)));` (plus any animated transforms)
    *   Where CSS custom properties `--x-offset` and `--y-offset` are defined for each element based on its Design Document X,Y coordinates:
        *   `--x-offset: [Design_Doc_X_Coordinate * (100/14)]vw;`
        *   `--y-offset: [Design_Doc_Y_Coordinate_Transformed_For_CSS * (100/8)]vh;`
        *   **Y-Coordinate CSS Transformation Note:** The Design Document specifies Y positive as UP. CSS `transform: translateY()` positive is DOWN. Therefore, if Design Doc Y is `Y_doc`, then for CSS transform the offset should be calculated using `Y_doc * -1` (or ensure the `var(--y-offset)` correctly reflects this inversion for upward movement). **Adhere strictly to the Y-axis convention specified in the Design Document's section `5. 重要说明与约束` under "坐标系与CSS转换".**

**2.2. Asset Handling (HTML & CSS):**
*   **Images:** Use `<img>` tags. `src` paths **must exactly match** the `资产路径` in the Design Document.
*   **Image Preloading (JavaScript):** Implement robust image preloading (e.g., `Promise.all` for all images with `Image.onload`) before initiating the animation timeline in JavaScript.
*   **Image Display (CSS):** Use `object-fit` (e.g., `cover` or `contain`) as specified in the `CSS object-fit 建议` for each image element in the Design Document. Apply `box-shadow` or `border-radius` **only if specified**.

**2.3. Text Elements (HTML & CSS):**
*   **Content & Structure:** Text content must be exactly as in the Design Document, placed within appropriate HTML tags (e.g., `<div>` or `<span>`) identified by their `元素ID`.
*   **Styling (CSS):**
    *   `font-family`, `color`, `text-align`, `font-weight`, `font-style`, `text-shadow`, `line-height` must strictly match the Design Document's specifications for each text element.
    *   **Font Size (CSS):** Implement `font-size` using the `vw` or `px` value specified in the Design Document. If `px`, it's assumed to be for a 1920px wide canvas, so convert appropriately if responsive scaling is intended, or use directly if fixed size is intended. The document might specify `(PX_VALUE / 19.2)vw`.

## 3. Animation System & Timeline (ABSOLUTE PRECISION REQUIRED):

*   **JavaScript Timeline Control:** Use `setTimeout` to manage the sequence of scenes/timeslices. Within each `setTimeout` callback corresponding to a new timeslice:
    *   The primary responsibility is to manage element visibility and trigger animations as per section `4. 分镜/场景与音频同步列表` of the Design Document.
*   **CSS for Animations & States:**
    *   Define CSS animations (`@keyframes`) and transition properties as described in the "动画与状态描述" for each element.
    *   Use CSS classes to manage states (e.g., `.visible`, `.hidden`, `.animating-in`, `.animating-out`, specific animation classes like `.slideInFromLeft-active`). JavaScript will primarily add/remove these classes.
    *   Ensure `animation-fill-mode: forwards;` is used for animations that need to persist their final state.
*   **Z-Index (CSS):** Manage `z-index` for elements if layering is specified or implied by the Design Document to prevent incorrect visual overlap.

**3.1. 분镜元素管理 (CRITICAL - To be handled by JavaScript in each timeslice's `setTimeout`):**
*   **For EACH timeslice defined in `## 4. 分镜/场景与音频同步列表` of the Design Document:**
    *   **新增元素:** For every `元素ID` listed under "**新增元素 (IDs)**":
        *   Ensure the corresponding HTML element becomes visible (e.g., remove a `.hidden` class, add an `.active` or `.visible` class).
        *   Trigger its specified **entry animation** (e.g., add an animation-triggering class).
    *   **移除元素:** For every `元素ID` listed under "**移除元素 (IDs)**":
        *   Trigger its specified **exit animation**.
        *   After the exit animation completes (or immediately if no exit animation), ensure the element is hidden (e.g., add a `.hidden` class, or set `display: none` if its animation is complete and it won't be reused soon).
    *   **持续显示元素:** For every `元素ID` listed under "**持续显示元素 (IDs)**":
        *   Ensure these elements remain visible.
        *   If the "动画与状态描述" for this timeslice indicates a **transformation or state change** for a persistent element, apply that change (e.g., trigger a new CSS animation/transition, or update its style directly via JS if necessary for dynamic properties).
    *   **Element Styles & Positions:** Ensure all *active* elements in the current timeslice (new, persistent) reflect the "当前尺寸", "当前位置", and "当前主要CSS样式" specified for them in that timeslice's "视觉构成与布局" section. This might involve JS updating CSS custom properties or classes if styles change dynamically beyond initial setup.

# CREATIVE ENHANCEMENT (Apply TASTEFULLY & ONLY where the Design Document is VAGUE or allows interpretation):

*Creative enhancements are secondary to strict document fidelity.* If the Design Document specifies an animation (e.g., "元素A: 使用CSS animation 'slideInFromLeft'"), implement that **exactly**.
If the document *only* states an element appears/disappears without detailing the transition, or if a style like `font-family` is merely "suggested" and not fixed, you may:
*   **Implement elegant default transitions:** Subtle fade-in/out (e.g., `transition: opacity 0.5s ease-in-out;`) if no specific animation is given for appearance/disappearance.
*   **Refine suggested styles:** If a font is "suggested," use it. If color is "suggested based on调性," make a sensible choice that fits the overall aesthetic described.
*   **Ensure smooth easing:** Default to common easing functions like `ease-in-out` or `cubic-bezier(0.4, 0, 0.2, 1)` for any self-implemented transitions to maintain a "cinematic feel."

**DO NOT:**
*   Introduce HTML elements (identified by `元素ID`) not listed in section 3 of the Design Document.
*   Omit any HTML element that *is* listed and should be active in a given timeslice.
*   Change specified timings, positions, dimensions, or core animation descriptions from the Design Document.
*   Over-animate; prioritize the Design Document's narrative clarity.

# AUDIO CUE COMMENTS:
In the JavaScript, at the precise time points or event triggers (e.g., start of an animation) indicated by audio cues in the Design Document's "动画与状态描述", insert clear comments:
`// AUDIO CUE: [Brief description of sound from Design Doc, e.g., "闪光音效 - 耳环旋转入场时"] - Trigger audio playback here.`

# QUALITY ASSURANCE & OUTPUT:
*   **Performance:** Aim for 60fps. Use CSS transforms and opacity for animations where possible. Minimize direct JS style manipulation in loops.
*   **Clarity & Maintainability:** Well-structured HTML, readable CSS, and heavily commented JavaScript, especially for the timeline logic and element state management within each `setTimeout`.
*   **Output:** A single, complete HTML file enclosed in a Markdown code block.

```html
<!DOCTYPE html>
<html lang="zh-CN"> <!-- Or lang from Design Doc -->
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><!-- Title From Design Doc's "1. 整体概念与目标" or a generic one if not specified --></title>
    <style>
        /* Global styles, animation-container, base element styles */
        /* CSS @keyframes and state classes (.hidden, .visible, .animation-specific-classes) */
    </style>
</head>
<body>
    <div class="animation-container">
        <!-- ALL potential visual elements (img, div for text) from Design Doc sections 3.1, 3.2, 3.4,
             each with its specified HTML 'id'. Initially hidden if not active in the first timeslice.
        -->
        <!-- Example: <img id="char_yhdt" src="..." class="character-element hidden"> -->
        <!-- Example: <div id="text_title" class="text-element hidden">...</div> -->
    </div>
    <script>
        // Element references (e.g., const yhdtEl = document.getElementById('char_yhdt');)
        // Image preloading logic
        // Timeline function using chained setTimeouts
        // Inside each setTimeout:
        //  - Logic to add/remove classes for visibility (for '新增元素', '移除元素')
        //  - Logic to trigger animations (add/remove animation classes for '新增元素', '移除元素', '持续显示元素' if they animate)
        //  - AUDIO CUE comments
    </script>
</body>
</html>
            """,
            history=[],
            query=animation_prompt
        )

    async def _process_html_response(self, item: Tuple[str, str], response: str, project_hash: str, project_dir: str) -> Tuple[str, str, str]:
        """
        处理HTML代码生成响应

        Args:
            item: 包含场景ID和提示词的元组
            response: LLM生成的响应
            project_hash: 项目哈希
            project_dir: 项目目录

        Returns:
            Tuple[str, str, str]: 包含(场景ID, HTML代码, S3路径)的元组
        """
        scene_id, scene_prompt = item
        max_retries = 5
        retry_count = 0

        while retry_count < max_retries:
            try:
                # 提取HTML代码
                html_code = ManimUtils.extract_manim_code(response)

                # 检查是否成功提取到代码内容
                if not html_code:
                    print(f"第{retry_count + 1}次尝试：未能提取到代码内容，将重新生成")
                    raise ValueError("代码提取失败，需要重新生成")

                print(f"成功提取HTML代码")

                # 保存HTML文件到本地并上传到S3
                s3_path = self._save_html_file(scene_id, html_code, project_hash, project_dir)

                return scene_id, html_code, s3_path

            except Exception as e:
                retry_count += 1
                if retry_count >= max_retries:
                    print(f"达到最大重试次数({max_retries})，生成失败")
                    raise e
                print(f"第{retry_count}次尝试失败，准备第{retry_count + 1}次重试")

    def _save_html_file(self, scene_id: str, html_code: str, project_hash: str, project_dir: str) -> str:
        """
        保存HTML文件到本地并上传到S3

        Args:
            scene_id: 场景ID
            html_code: HTML代码
            project_hash: 项目哈希
            project_dir: 项目目录

        Returns:
            str: S3路径
        """
        # 保存HTML文件到本地
        html_filename = f"scene_{scene_id}.html"
        local_html_path = os.path.join(self.html_output_dir, project_hash, html_filename)
        with open(local_html_path, "w", encoding="utf-8") as f:
            f.write(html_code)
        print(f"已保存场景 {scene_id} 的HTML文件到本地: {local_html_path}")

        # 构建S3路径
        s3_html_path = f"{project_dir}/{html_filename}"

        # 上传HTML文件到S3
        try:
            self.s3_handler.put_object_by_content(s3_html_path, html_code)
            print(f"已上传场景 {scene_id} 的HTML文件到S3: {s3_html_path}")
        except Exception as e:
            print(f"上传HTML文件到S3失败: {e}")

        return s3_html_path

    def _save_script_file(self, state: Dict[str, Any], project_dir: str, project_hash: str) -> str:
        """
        保存脚本文件到本地和S3

        Args:
            state: 当前状态
            project_dir: 项目目录
            project_hash: 项目哈希

        Returns:
            str: 数据状态路径
        """
        # 保存脚本文件
        data_state_content = state["data"]
        data_state_path = f"{project_dir}/data_state.json"
        local_data_state_path = os.path.join(self.html_output_dir, project_hash, "data_state.json")

        # 保存脚本到本地
        # 创建目录路径（如果不存在）
        os.makedirs(os.path.dirname(local_data_state_path), exist_ok=True)

        # 直接使用open写入，避免使用file_manager.write_json
        with open(local_data_state_path, "w", encoding="utf-8") as f:
            json.dump(data_state_content, f, ensure_ascii=False, indent=2)

        # 上传脚本到S3
        try:
            self.s3_handler.put_object_by_content(data_state_path, json.dumps(data_state_content, ensure_ascii=False, indent=2), mimetype="application/json")
            print(f"已上传脚本文件到S3: {data_state_path}")
        except Exception as e:
            print(f"上传脚本文件到S3失败: {e}")

        return data_state_path

    def _update_state_with_results(self, state: Dict[str, Any], results: List[Tuple[str, str, str]]) -> List[Tuple[str, str]]:
        """
        更新状态中的HTML文件信息

        Args:
            state: 当前状态
            results: 生成结果列表

        Returns:
            List[Tuple[str, str]]: 场景路径列表，每项包含(场景ID, S3路径)
        """
        # 更新状态
        state["data"]["html_files"] = state["data"].get("html_files", {})
        state["data"]["html_paths"] = state["data"].get("html_paths", {})

        # 将结果添加到状态中
        scene_paths = []
        for scene_id, html_code, s3_path in results:
            state["data"]["html_files"][scene_id] = html_code
            state["data"]["html_paths"][scene_id] = s3_path
            scene_paths.append((scene_id, s3_path))

            # 将HTML代码和路径添加到news_report的对应分镜中
            self._update_news_report_with_html(state, scene_id, html_code, s3_path)

        return scene_paths

    def _update_news_report_with_html(self, state: Dict[str, Any], scene_id: str, html_code: str, s3_path: str) -> None:
        """
        将HTML代码和路径添加到news_report的对应分镜中

        Args:
            state: 当前状态
            scene_id: 场景ID
            html_code: HTML代码
            s3_path: S3路径
        """
        # 获取news_report
        news_report = state["data"].get("news_report", {})
        sections = news_report.get("sections", [])

        # 解析场景ID
        if "_shot_" in scene_id:
            # 镜头格式: section_X_shot_Y
            parts = scene_id.split('_')
            section_index = int(parts[1]) - 1
            shot_index = int(parts[3]) - 1

            # 确保索引有效
            if 0 <= section_index < len(sections) and "subsections" in sections[section_index]:
                subsections = sections[section_index].get("subsections", [])
                if 0 <= shot_index < len(subsections):
                    # 更新子镜头的html_code和html_path
                    sections[section_index]["subsections"][shot_index]["html_code"] = html_code
                    sections[section_index]["subsections"][shot_index]["html_path"] = s3_path
                    print(f"已更新 {scene_id} 的html_code和html_path")
        else:
            # 普通场景格式: section_X
            section_index = int(scene_id.split('_')[1]) - 1
            if 0 <= section_index < len(sections):
                # 更新场景的html_code和html_path
                sections[section_index]["html_code"] = html_code
                sections[section_index]["html_path"] = s3_path
                print(f"已更新 {scene_id} 的html_code和html_path")

        # 更新news_report
        state["data"]["news_report"] = news_report