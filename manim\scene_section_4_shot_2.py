from manim import *
import os
from layout_manager import LayoutManager

'''
========== 动画素材清单 ==========

## 图片素材：
- 东阳市横店黄杨钿甜影视工作室: image.prop - 黄杨钿甜父亲成立的工作室

## 文字关键词素材：
- "演艺疑问" - 0s-6s - 微软雅黑加粗，深灰色
- "黄杨钿甜" - 0s-6s - 微软雅黑，深灰色
- "17岁" - 3s-6s - 等宽字体加粗，蓝色
- "33部影视作品" - 3s-6s - 等宽字体加粗，红色
- "丝滑" - 3s-6s - 微软雅黑，黄色
- "2018年" - 6s-10s - 微软雅黑，蓝色
- "父亲辞职" - 6s-10s - 微软雅黑，深灰色
- "成立影视工作室" - 6s-10s - 微软雅黑，深灰色
- "时间点高度重合" - 10s-13s - 微软雅黑加粗，红色
- "出道" - 10s-13s - 微软雅黑，深灰色
- "公职身份" - 10s-13s - 微软雅黑，黄色高亮
- "人脉" - 10s-13s - 微软雅黑，黄色高亮
- "铺路" - 10s-13s - 微软雅黑，深灰色
- "演艺行业公平竞争？" - 13s-15s - 微软雅黑加粗，深灰色
- "?" - 13s-15s - 微软雅黑加粗，红色

## 动画总时长：15秒
=====================================
'''

class YellowYangDiantianAnimation(Scene):
    def construct(self):
        # 设置背景色
        self.camera.background_color = "#FFFFFF"
        
        # 检查图片资源是否存在
        image_path = "image.prop"
        if not os.path.exists(image_path):
            print(f"警告: 图片文件 {image_path} 不存在!")
            # 创建一个替代的矩形作为图片占位符
            studio_image = Rectangle(height=1.7, width=3, color=GRAY).set_opacity(0.5)
        else:
            # 加载工作室图片
            studio_image = ImageMobject(image_path)
            # 根据宽高比设置尺寸 (宽高比 1.78)
            studio_image.height = 1.7
            studio_image.width = 1.7 * 1.78
        
        studio_image.set_opacity(0)  # 初始透明
        
        # 创建文字元素
        title = Text("演艺疑问", font="SimHei", font_size=28, color="#333333", weight=BOLD).set_opacity(0)
        name = Text("黄杨钿甜", font="SimHei", font_size=22, color="#333333").set_opacity(0)
        
        age = Text("17岁", font="Courier New", font_size=32, color=BLUE, weight=BOLD).set_opacity(0)
        works = Text("33部影视作品", font="Courier New", font_size=32, color=RED, weight=BOLD).set_opacity(0)
        smooth = Text("丝滑", font="SimHei", font_size=24, color=YELLOW).set_opacity(0)
        
        year = Text("2018年", font="SimHei", font_size=24, color=BLUE).set_opacity(0)
        father_resign = Text("父亲辞职", font="SimHei", font_size=20, color="#333333").set_opacity(0)
        studio_establish = Text("成立影视工作室", font="SimHei", font_size=20, color="#333333").set_opacity(0)
        
        time_overlap = Text("时间点高度重合", font="SimHei", font_size=24, color=RED, weight=BOLD).set_opacity(0)
        debut = Text("出道", font="SimHei", font_size=20, color="#333333").set_opacity(0)
        official_position = Text("公职身份", font="SimHei", font_size=22, color=YELLOW).set_opacity(0)
        connections = Text("人脉", font="SimHei", font_size=22, color=YELLOW).set_opacity(0)
        pave_way = Text("铺路", font="SimHei", font_size=22, color="#333333").set_opacity(0)
        
        fair_competition = Text("演艺行业公平竞争？", font="SimHei", font_size=26, color="#333333", weight=BOLD).set_opacity(0)
        question_mark = Text("?", font="SimHei", font_size=36, color=RED, weight=BOLD).set_opacity(0)
        
        # 创建时间轴
        timeline = Line(start=[-5, 3], end=[-5, -3], color=GREY)
        timeline.set_opacity(0)
        
        # 设置元素位置
        title.move_to([0, 3.5, 0])
        name.move_to([0, 3, 0])
        
        age.move_to([-4, 1, 0])
        works.move_to([3, 1, 0])
        smooth.move_to([0, 0, 0])
        
        year.move_to([-5, 0, 0])
        father_resign.move_to([-3, 0.5, 0])
        studio_establish.move_to([-3, -0.5, 0])
        
        time_overlap.move_to([0, 2, 0])
        debut.move_to([-3, 0, 0])
        official_position.move_to([-5, -2, 0])
        connections.move_to([-3, -2, 0])
        pave_way.move_to([0, -2, 0])
        
        fair_competition.move_to([0, -3, 0])
        question_mark.move_to([0, 0, 0])
        
        # 确保所有元素在屏幕边界内
        for element in [title, name, age, works, smooth, year, father_resign, studio_establish, 
                       time_overlap, debut, official_position, connections, pave_way, 
                       fair_competition, question_mark]:
            LayoutManager.ensure_screen_bounds(element)
        
        # 第一阶段 (0s-3s): 介绍黄杨钿甜
        # 工作室图片放在右下角
        studio_image.move_to([5, -2, 0])
        
        # 打字机效果显示标题
        self.play(
            Write(title),
            run_time=1
        )
        
        self.play(
            FadeIn(name),
            FadeIn(timeline),
            FadeIn(studio_image),
            run_time=1
        )
        
        self.wait(1)
        
        # 第二阶段 (3s-6s): 显示年龄和作品数量
        self.play(
            FadeIn(age, scale=1.2),
            run_time=0.5
        )
        
        # 计数器效果显示作品数量
        counter = Integer(0)
        counter.move_to(works.get_center())
        counter.set_color(RED)
        counter.font_size = 32
        
        self.play(
            FadeIn(counter),
            run_time=0.5
        )
        
        self.play(
            ChangeDecimalToValue(counter, 33),
            run_time=1
        )
        
        self.play(
            FadeOut(counter),
            FadeIn(works),
            run_time=0.2
        )
        
        # 显示"丝滑"并添加轻微摇晃效果
        self.play(
            FadeIn(smooth),
            smooth.animate.scale(1.1).shift(UP*0.1),
            run_time=0.5
        )
        
        self.play(
            smooth.animate.scale(1/1.1).shift(DOWN*0.1),
            run_time=0.3
        )
        
        self.wait(1)
        
        # 第三阶段 (6s-10s): 父亲辞职成立工作室
        self.play(
            FadeOut(age),
            FadeOut(works),
            FadeOut(smooth),
            FadeOut(title),
            FadeOut(name),
            # 工作室图片放大并移至中央
            studio_image.animate.set_opacity(1).scale(2.33).move_to([0, 0, 0]),
            run_time=1
        )
        
        # 在时间轴上显示2018年节点
        year_dot = Dot(point=[-5, 0, 0], color=BLUE)
        
        self.play(
            FadeIn(year),
            FadeIn(year_dot),
            run_time=1
        )
        
        # 显示父亲辞职和成立工作室
        self.play(
            FadeIn(father_resign),
            FadeIn(studio_establish),
            run_time=1
        )
        
        self.wait(1)
        
        # 第四阶段 (10s-13s): 时间点重合与质疑
        # 添加出道节点
        debut_dot = Dot(point=[-5, -1, 0], color=GREY)
        
        # 创建连接线
        connection_line = DashedLine(start=[-5, 0, 0], end=[-5, -1, 0], color=RED)
        
        self.play(
            FadeIn(debut),
            FadeIn(debut_dot),
            FadeIn(connection_line),
            run_time=1
        )
        
        self.play(
            FadeIn(time_overlap),
            run_time=0.5
        )
        
        # 显示公职身份和人脉
        self.play(
            FadeIn(official_position),
            FadeIn(connections),
            run_time=0.5
        )
        
        # 创建从"人脉"指向"铺路"的箭头
        arrow = Arrow(start=connections.get_right(), end=pave_way.get_left(), color=ORANGE)
        
        self.play(
            FadeIn(pave_way),
            FadeIn(arrow),
            run_time=1
        )
        
        # 第五阶段 (13s-15s): 引发公平竞争问题讨论
        self.play(
            FadeOut(year),
            FadeOut(year_dot),
            FadeOut(father_resign),
            FadeOut(studio_establish),
            FadeOut(debut),
            FadeOut(debut_dot),
            FadeOut(connection_line),
            FadeOut(time_overlap),
            FadeOut(official_position),
            FadeOut(connections),
            FadeOut(pave_way),
            FadeOut(arrow),
            # 工作室图片变半透明
            studio_image.animate.set_opacity(0.3),
            run_time=0.5
        )
        
        # 显示问号和公平竞争问题
        self.play(
            FadeIn(question_mark, scale=0.1),
            question_mark.animate.scale(10),
            run_time=0.5
        )
        
        self.play(
            FadeIn(fair_competition),
            run_time=0.5
        )
        
        # 最后定格2秒
        self.wait(1.5)