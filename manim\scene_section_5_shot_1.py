from manim import *
import os
from layout_manager import LayoutManager

'''
========== 动画素材清单 ==========

## 图片素材：
- 金币/奢侈品图标: [未提供路径] - 象征炫富的金币堆或奢侈品物件图标
- 放大镜/档案图标: [未提供路径] - 代表调查与挖掘背景的放大镜或档案图标
- 官方声明图标: [未提供路径] - 代表官方回应的公告或麦克风图标
- 社交媒体评论图标: [未提供路径] - 代表舆论反应的社交媒体评论图标
- 天平/监督符号图标: [未提供路径] - 代表权力监督的天平或监督符号图标
- 流程图箭头: [未提供路径] - 连接各个阶段的箭头图标

## 文字关键词素材：
- "固定模式：从炫富到落马的社会监督链条" - 0s-20s - 黑体加粗，36pt，黑色
- "明星炫富" - 0s-20s - 无衬线黑体，24pt，黑色
- "家族背景挖掘" - 3s-20s - 无衬线黑体，24pt，黑色
- "官方有限回应" - 6s-20s - 无衬线黑体，24pt，黑色
- "舆论不满反弹" - 10s-20s - 无衬线黑体，24pt，黑色
- "权力监督讨论" - 14s-20s - 无衬线黑体，24pt，黑色
- "#北极鲶鱼案" - 3s-6s - 蓝色无衬线体，24pt
- "#曲婉婷母亲案" - 6s-10s - 蓝色无衬线体，24pt
- "#周劼事件" - 10s-14s - 蓝色无衬线体，24pt
- "多起'子女炫富导致官员落马'案例" - 14s-18s - 蓝色无衬线体，24pt
- "公众监督，权力透明" - 18s-20s - 加粗无衬线黑体，32pt，黑色

## 动画总时长：20秒
=====================================
'''

class SocialSupervisionChain(Scene):
    def construct(self):
        # 设置背景色（根据设计文档）
        self.camera.background_color = "#FFFFFF"
        
        # 创建主标题
        title = Text("固定模式：从炫富到落马的社会监督链条", 
                    font="SimHei", 
                    font_size=36, 
                    color=BLACK, 
                    weight=BOLD)
        title.to_edge(UP, buff=0.5)
        title.set_opacity(0)
        
        # 创建图标（由于未提供路径，使用Manim内置形状代替）
        # 1. 金币/奢侈品图标
        luxury_icon = Circle(radius=1, color=GOLD_E, fill_opacity=1)
        dollar_sign = Text("$", font_size=48, color=WHITE).move_to(luxury_icon.get_center())
        luxury_group = VGroup(luxury_icon, dollar_sign)
        luxury_group.move_to([-5.6, 0, 0])
        luxury_group.set_opacity(0)
        
        # 2. 放大镜/档案图标
        magnifier = Circle(radius=0.8, color=BLUE_D, fill_opacity=0.2)
        handle = Rectangle(height=0.8, width=0.2, color=BLUE_D, fill_opacity=1).next_to(magnifier, DOWN+RIGHT, buff=0)
        handle.rotate(PI/4, about_point=magnifier.get_corner(DR))
        background_search = VGroup(magnifier, handle)
        background_search.move_to([-2.8, 0, 0])
        background_search.set_opacity(0)
        
        # 3. 官方声明图标
        official_response = RoundedRectangle(height=1.5, width=1.2, corner_radius=0.2, color=RED_D, fill_opacity=0.8)
        lines = VGroup(
            Line([-0.4, 0.3, 0], [0.4, 0.3, 0], color=WHITE),
            Line([-0.4, 0, 0], [0.4, 0, 0], color=WHITE),
            Line([-0.4, -0.3, 0], [0.2, -0.3, 0], color=WHITE)
        ).scale(0.8)
        official_icon = VGroup(official_response, lines)
        official_icon.move_to([0, 0, 0])
        official_icon.set_opacity(0)
        
        # 4. 社交媒体评论图标
        comment_bubble = SVGMobject("""
        <svg width="100" height="100" xmlns="http://www.w3.org/2000/svg">
          <path d="M10,10 L90,10 L90,70 L50,70 L30,90 L30,70 L10,70 Z" fill="none" stroke="black" stroke-width="4"/>
        </svg>
        """, color=GREEN_D, fill_opacity=0.8)
        dots = VGroup(
            Dot(color=WHITE).shift(LEFT*0.5),
            Dot(color=WHITE),
            Dot(color=WHITE).shift(RIGHT*0.5)
        ).scale(0.8)
        social_media_icon = VGroup(comment_bubble, dots)
        social_media_icon.move_to([2.8, 0, 0])
        social_media_icon.set_opacity(0)
        
        # 5. 天平/监督符号图标
        base = Line([-0.8, -0.5, 0], [0.8, -0.5, 0], color=PURPLE_D)
        stand = Line([0, -0.5, 0], [0, 0.5, 0], color=PURPLE_D)
        beam = Line([-0.8, 0.5, 0], [0.8, 0.5, 0], color=PURPLE_D)
        left_scale = Circle(radius=0.3, color=PURPLE_D, fill_opacity=0.8).move_to([-0.8, 0.2, 0])
        right_scale = Circle(radius=0.3, color=PURPLE_D, fill_opacity=0.8).move_to([0.8, 0.2, 0])
        balance_icon = VGroup(base, stand, beam, left_scale, right_scale)
        balance_icon.move_to([5.6, 0, 0])
        balance_icon.set_opacity(0)
        
        # 创建箭头
        arrows = []
        arrow_positions = [[-4.2, 0, 0], [-1.4, 0, 0], [1.4, 0, 0], [4.2, 0, 0]]
        for pos in arrow_positions:
            arrow = Arrow(start=pos - np.array([0.8, 0, 0]), end=pos + np.array([0.8, 0, 0]), 
                         color=GRAY, buff=0.1, stroke_width=3)
            arrow.set_opacity(0)
            arrows.append(arrow)
        
        # 创建阶段文字标签
        stage_labels = []
        stage_texts = ["明星炫富", "家族背景挖掘", "官方有限回应", "舆论不满反弹", "权力监督讨论"]
        stage_positions = [[-5.6, -1, 0], [-2.8, -1, 0], [0, -1, 0], [2.8, -1, 0], [5.6, -1, 0]]
        
        for text, pos in zip(stage_texts, stage_positions):
            label = Text(text, font="SimHei", font_size=24, color=BLACK)
            label.move_to(pos)
            label.set_opacity(0)
            stage_labels.append(label)
        
        # 创建案例标签
        case_labels = []
        case_texts = ["#北极鲶鱼案", "#曲婉婷母亲案", "#周劼事件", "多起'子女炫富导致官员落马'案例"]
        
        for text in case_texts:
            label = Text(text, font="SimHei", font_size=24, color=BLUE_D)
            label.move_to([0, -3, 0])
            label.set_opacity(0)
            case_labels.append(label)
        
        # 创建结论文字
        conclusion = Text("公众监督，权力透明", font="SimHei", font_size=32, color=BLACK, weight=BOLD)
        conclusion.move_to([0, -2, 0])
        conclusion.set_opacity(0)
        
        # 开始动画序列
        
        # 0.0s ~ 3.0s: 第一阶段 - 明星炫富
        self.play(
            FadeIn(title, shift=DOWN),
            run_time=1
        )
        
        self.play(
            FadeIn(luxury_group, scale=1.2),
            FadeIn(stage_labels[0]),
            run_time=1
        )
        
        # 确保布局安全
        LayoutManager.ensure_screen_bounds(luxury_group)
        LayoutManager.ensure_screen_bounds(stage_labels[0])
        LayoutManager.print_layout_debug(luxury_group, "奢侈品图标")
        
        self.wait(1)
        
        # 3.0s ~ 6.0s: 第二阶段 - 家族背景挖掘
        self.play(
            FadeIn(arrows[0]),
            FadeIn(background_search, scale=1.2),
            FadeIn(stage_labels[1]),
            FadeIn(case_labels[0]),
            run_time=1.5
        )
        
        # 确保布局安全
        LayoutManager.ensure_screen_bounds(background_search)
        LayoutManager.ensure_screen_bounds(stage_labels[1])
        LayoutManager.ensure_screen_bounds(case_labels[0])
        LayoutManager.print_layout_debug(background_search, "背景挖掘图标")
        
        self.wait(1.5)
        
        # 6.0s ~ 10.0s: 第三阶段 - 官方有限回应
        self.play(
            FadeIn(arrows[1]),
            FadeIn(official_icon, scale=1.2),
            FadeIn(stage_labels[2]),
            FadeOut(case_labels[0]),
            FadeIn(case_labels[1]),
            run_time=1.5
        )
        
        # 确保布局安全
        LayoutManager.ensure_screen_bounds(official_icon)
        LayoutManager.ensure_screen_bounds(stage_labels[2])
        LayoutManager.ensure_screen_bounds(case_labels[1])
        LayoutManager.print_layout_debug(official_icon, "官方回应图标")
        
        self.wait(2.5)
        
        # 10.0s ~ 14.0s: 第四阶段 - 舆论不满反弹
        self.play(
            FadeIn(arrows[2]),
            FadeIn(social_media_icon, scale=1.2),
            FadeIn(stage_labels[3]),
            FadeOut(case_labels[1]),
            FadeIn(case_labels[2]),
            run_time=1.5
        )
        
        # 确保布局安全
        LayoutManager.ensure_screen_bounds(social_media_icon)
        LayoutManager.ensure_screen_bounds(stage_labels[3])
        LayoutManager.ensure_screen_bounds(case_labels[2])
        LayoutManager.print_layout_debug(social_media_icon, "社交媒体图标")
        
        self.wait(2.5)
        
        # 14.0s ~ 18.0s: 第五阶段 - 权力监督讨论
        self.play(
            FadeIn(arrows[3]),
            FadeIn(balance_icon, scale=1.2),
            FadeIn(stage_labels[4]),
            FadeOut(case_labels[2]),
            FadeIn(case_labels[3]),
            run_time=1.5
        )
        
        # 确保布局安全
        LayoutManager.ensure_screen_bounds(balance_icon)
        LayoutManager.ensure_screen_bounds(stage_labels[4])
        LayoutManager.ensure_screen_bounds(case_labels[3])
        LayoutManager.print_layout_debug(balance_icon, "权力监督图标")
        
        self.wait(2.5)
        
        # 18.0s ~ 20.0s: 总结阶段
        # 轻微拉远镜头效果
        all_elements = VGroup(
            luxury_group, background_search, official_icon, 
            social_media_icon, balance_icon,
            *arrows, *stage_labels
        )
        
        self.play(
            all_elements.animate.scale(0.95),
            FadeOut(case_labels[3]),
            run_time=0.5
        )
        
        # 打字机效果显示结论
        self.play(AddTextLetterByLetter(conclusion), run_time=1)
        
        # 最终高亮整个流程图
        self.play(
            all_elements.animate.set_color(YELLOW).set_color(BLACK),
            rate_func=there_and_back_with_pause,
            run_time=0.5
        )
        
        self.wait(0)