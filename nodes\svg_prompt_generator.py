import os
from typing import Dict, Any, List, Tuple
from langchain_core.messages import BaseMessage

from utils.prompt_utils import PromptManager
from llm.claude_local import ClaudeLocalProvider
from llm.batch_processor import BatchProcessor

class SVGPromptGenerator:
    """SVG提示词生成节点"""

    def __init__(self):
        """初始化SVG提示词生成节点"""
        self.prompt_manager = PromptManager()
        self.llm = ClaudeLocalProvider()
        self.batch_processor = BatchProcessor(provider=self.llm, max_concurrent=10)

    async def process(self, state: Dict[str, Any]) -> Dict[str, Any]:
        """
        处理数据

        Args:
            state: 当前状态

        Returns:
            Dict[str, Any]: 更新后的状态
        """
        print("\n" + "="*50)
        print("【开始执行】SVG提示词生成 (SVGPromptGenerator)")
        print("="*50)

        # 初始化环境和加载提示词模板
        svg_prompt_dir, system_prompt = self._init_environment()
        
        # 收集场景和镜头信息，准备批处理数据
        batch_items = self._collect_scenes_and_shots(state)
        
        # 执行批处理生成提示词
        results = await self._generate_prompts_batch(batch_items, system_prompt)
        
        # 更新状态
        self._update_state_with_results(state, results)

        print("="*50)
        print("【完成执行】SVG提示词生成 (SVGPromptGenerator)")
        print("="*50 + "\n")

        return state
        
    def _init_environment(self) -> Tuple[str, str]:
        """
        初始化环境和加载提示词模板
        
        Returns:
            Tuple[str, str]: 提示词目录路径和系统提示词
        """
        # 获取提示词模板
        system_prompt = self.prompt_manager.get_prompt("4")
        print("已加载提示词模板")
        
        return "", system_prompt
        
    def _collect_scenes_and_shots(self, state: Dict[str, Any]) -> List[Tuple[str, str, str]]:
        """
        收集场景和镜头信息，准备批处理数据
        
        Args:
            state: 当前状态
            
        Returns:
            List[Tuple[str, str, str]]: 批处理项列表，每项包含(场景ID, 场景标题, 查询内容)
        """
        # 获取分析报告中的场景数据
        news_report = state["data"]["news_report"]
        sections = news_report.get("sections", [])
        print(f"从分析报告中获取到 {len(sections)} 个场景")
        
        # 准备批处理数据
        batch_items = []

        # 收集所有需要处理的场景和镜头
        shot_count = 0
        for i, section in enumerate(sections):
            section_id = f"section_{i+1}"
            section_title = section.get("title", "未命名场景")
            section_content = section.get("content", "")

            # 获取当前场景的子镜头列表
            subsections = section.get("subsections", [])

            if subsections:
                # 如果有子镜头，则为每个子镜头生成提示词
                for j, subsection in enumerate(subsections):
                    shot_count += 1
                    shot_id = f"{section_id}_shot_{j+1}"
                    batch_item = self._prepare_shot_batch_item(section, section_id, section_title, section_content, subsection, j)
                    batch_items.append(batch_item)
                    print(f"准备处理场景 {section_id} 的镜头 {j+1}: {batch_item[1]}")

        print(f"总共准备处理 {len(batch_items)} 个场景/镜头的SVG提示词生成")
        return batch_items
        
    def _prepare_shot_batch_item(self, section: Dict[str, Any], section_id: str, section_title: str, 
                              section_content: str, subsection: Dict[str, Any], shot_index: int) -> Tuple[str, str, str]:
        """
        准备镜头的批处理项
        
        Args:
            section: 场景数据
            section_id: 场景ID
            section_title: 场景标题
            section_content: 场景内容
            subsection: 子镜头数据
            shot_index: 镜头索引
            
        Returns:
            Tuple[str, str, str]: 批处理项，包含(镜头ID, 镜头标题, 查询内容)
        """
        shot_id = f"{section_id}_shot_{shot_index+1}"
        shot_title = subsection.get("title", f"镜头{shot_index+1}")
        shot_content = subsection.get("content", "")
        shot_details = subsection.get("details", [])
        shot_assets = subsection.get("assets", [])
        
        # 获取对话记录
        conversation_log = subsection.get("conversation_log", [])
        conversation_text = self._format_conversation_log(conversation_log)

        # 构建当前场景的公共信息
        current_scene_title = section_title
        current_shot_content = section_content
        current_shot_assets = shot_assets

        print(f"当前场景的公共信息: {current_scene_title}, {current_shot_content}, {current_shot_assets}")

        # 构建当前镜头的信息
        current_shot_title = shot_title
        current_shot_content = shot_content
        current_shot_details = "\n".join([f"- {detail}" for detail in shot_details])

        # 使用conversation_text作为query
        query = conversation_text
        
        return (shot_id, current_shot_title, query)
        
    def _format_conversation_log(self, conversation_log: List[Dict[str, str]]) -> str:
        """
        格式化对话记录
        
        Args:
            conversation_log: 对话记录列表
            
        Returns:
            str: 格式化后的对话文本
        """
        conversation_text = ""
        if not conversation_log:
            return "没有可用的对话记录"
            
        # 添加标题
        conversation_text += "# 导演与剪辑师对话记录\n\n"
        
        for log in conversation_log:
            role = log.get("role", "")
            content = log.get("content", "")
            
            # 角色映射
            if role == "user":
                role_title = "# 素材和指导信息"
            elif role == "视频剪辑师":
                role_title = "## 剪辑师方案"
            elif role == "视频导演":
                role_title = "## 导演反馈"
            else:
                role_title = f"## {role}"
                
            # 添加角色标题和内容
            conversation_text += f"{role_title}\n{content}\n\n"
            
        return conversation_text
        
    async def _generate_prompts_batch(self, batch_items: List[Tuple[str, str, str]], system_prompt: str) -> List[Tuple[str, str]]:
        """
        执行批处理生成提示词
        
        Args:
            batch_items: 批处理项列表
            system_prompt: 系统提示词
            
        Returns:
            List[Tuple[str, str]]: 结果列表，每项包含(场景ID, 生成的提示词)
        """
        # 执行批处理（启用深度思考）
        results = await self.batch_processor.process_batch(
            items=batch_items,
            create_messages_func=lambda item: self._create_messages_for_scene(item, system_prompt),
            process_response_func=self._process_scene_response,
            max_retries=5
        )
        
        return results
        
    def _create_messages_for_scene(self, item: Tuple[str, str, str], system_prompt: str) -> List[BaseMessage]:
        """
        创建场景提示词生成的消息
        
        Args:
            item: 批处理项
            system_prompt: 系统提示词
            
        Returns:
            List[BaseMessage]: 消息列表
        """
        scene_id, scene_title, query = item
        print(f"准备处理场景: {scene_id} - {scene_title}")
        return self.llm.create_messages(
            system_prompt=system_prompt,
            history=[],
            query=query
        )
        
    def _process_scene_response(self, item: Tuple[str, str, str], response: str) -> Tuple[str, str]:
        """
        处理场景响应
        
        Args:
            item: 批处理项
            response: LLM生成的响应
            
        Returns:
            Tuple[str, str]: 包含(场景ID, 生成的提示词)的元组
        """
        scene_id, scene_title, _ = item

        # 检查响应是否有效
        if not response or len(response) <= 100:
            print(f"生成的SVG提示词无效，使用默认提示词")
            response = f"# 场景 {scene_id} SVG动画设计\n\n无法生成有效的SVG动画设计文档。请检查输入数据或模型配置。"

        print(f"已生成场景 {scene_id} 的SVG提示词")

        return scene_id, response
        
    def _update_state_with_results(self, state: Dict[str, Any], results: List[Tuple[str, str]]) -> None:
        """
        更新状态中的SVG提示词信息
        
        Args:
            state: 当前状态
            results: 结果列表
        """
        # 更新状态
        state["messages"] = []  # 重置消息历史，避免消息过长

        # 将结果添加到状态中
        for scene_id, response in results:
            # 将SVG提示词添加到news_report的对应分镜中
            self._update_news_report_with_svg_prompts(state, scene_id, response)

        state["current_step"] = "generate_svg_prompt"
        
    def _update_news_report_with_svg_prompts(self, state: Dict[str, Any], scene_id: str, svg_prompt: str) -> None:
        """
        将SVG提示词添加到news_report的对应分镜中，并保存到文件
        
        Args:
            state: 当前状态
            scene_id: 场景ID
            svg_prompt: SVG提示词
        """
        # 获取news_report
        news_report = state["data"].get("news_report", {})
        sections = news_report.get("sections", [])
        
        # 解析场景ID
        if "_shot_" in scene_id:
            # 镜头格式: section_X_shot_Y
            parts = scene_id.split('_')
            section_index = int(parts[1]) - 1
            shot_index = int(parts[3]) - 1
            
            # 确保索引有效
            if 0 <= section_index < len(sections) and "subsections" in sections[section_index]:
                subsections = sections[section_index].get("subsections", [])
                if 0 <= shot_index < len(subsections):
                    # 更新子镜头的svg_prompt
                    sections[section_index]["subsections"][shot_index]["svg_prompt"] = svg_prompt
                    print(f"已更新 {scene_id} 的svg_prompt")
                    
                    # 保存到文件
                    prompt_file_path = os.path.join("data", "svg_prompt", f"{scene_id}.md")
                    os.makedirs(os.path.dirname(prompt_file_path), exist_ok=True)
                    with open(prompt_file_path, "w", encoding="utf-8") as f:
                        f.write(svg_prompt)
                    print(f"已将提示词保存到文件: {prompt_file_path}")
        else:
            # 普通场景格式: section_X
            section_index = int(scene_id.split('_')[1]) - 1
            if 0 <= section_index < len(sections):
                # 更新场景的svg_prompt
                sections[section_index]["svg_prompt"] = svg_prompt
                print(f"已更新 {scene_id} 的svg_prompt")
                
                # 保存到文件
                prompt_file_path = os.path.join("data", "svg_prompt", f"{scene_id}.md")
                os.makedirs(os.path.dirname(prompt_file_path), exist_ok=True)
                with open(prompt_file_path, "w", encoding="utf-8") as f:
                    f.write(svg_prompt)
                print(f"已将提示词保存到文件: {prompt_file_path}")
        
        # 更新news_report
        state["data"]["news_report"] = news_report

if __name__ == "__main__":
    generator = SVGPromptGenerator()

    generator.process(state_data)
