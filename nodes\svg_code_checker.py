import os
import asyncio
from typing import Dict, Any, List, Tuple, Optional
import json

from llm.claude_local import ClaudeLocalProvider
from llm.batch_processor import BatchProcessor
from llm.ai_conversation import AIConversation
from utils.file_utils import FileManager
from utils.svg_utils import SVGUtils  # 导入SVGUtils工具类
from utils.db_utils import write_project_video_info, write_project_video_scene, write_project_video_scene_material  # 导入数据库工具函数
from utils.db_pool import DBConnectionPool  # 导入数据库连接池

class SVGCodeChecker:
    """SVG代码和prompt一致性检查节点"""

    def __init__(self):
        """初始化SVG代码检查节点"""
        self.llm = ClaudeLocalProvider()
        self.batch_processor = BatchProcessor(provider=self.llm, max_concurrent=5)
        self.file_manager = FileManager()
        self.svg_utils = SVGUtils()  # 初始化SVG工具类
        self.conf_path = "../conf/conf.ini"  # 配置文件路径
        self.project_id = None  # 项目ID
        
        # 初始化数据库连接池
        self.db_pool = DBConnectionPool(self.conf_path)
        
        # 创建对话结果目录
        self.conversation_dir = "data/svg_reviews"
        os.makedirs(self.conversation_dir, exist_ok=True)
        
        # AI角色的系统提示词
        self.ai1_system_prompt = """
            你是一个review svg代码和prompt的一致性问题高手。

            你的任务是检查输入的SVG代码与prompt是否一致，并列出所有差异之处。
            
            你将收到每个分镜的svg代码和prompt。请仔细分析并对比这两者，
            找出任何不一致的地方。对于识别出的不一致，请根据其对画面核心信息传达和主要结构完整性的影响，分为以下两个等级：

            **不一致等级定义：**
            *   **[核心不一致]**: 直接影响**脚本核心信息传达**或**画面主要结构完整性**的差异。例如：
                *   关键元素（如主要角色、重要道具、核心文本）缺失或多余。
                *   关键元素的大小、位置严重偏离prompt描述，导致信息误读或画面结构混乱。
                *   核心动画效果与要求完全不匹配，影响事件的表达。
                *   文本内容错误或缺失关键信息。
                *   整体布局严重偏离prompt设计，导致信息层级不清或视觉焦点错误。
            *   **[次要不一致]**: 属于**视觉细节、审美或非核心功能**上的差异。例如：
                *   非关键元素的轻微位置或大小偏差，不影响主要信息传达。
                *   颜色、字体等样式细节与prompt描述不完全一致，但整体风格和可读性不受影响。
                *   非核心动画效果的微调或次要属性差异。
                *   SVG代码结构、命名习惯等与prompt没有直接描述但可能影响维护性的问题。

            **输出要求：**
            请以结构化的方式列出所有差异，清晰说明每个问题的具体位置和修改建议。
            *   对于每个不一致，请在其描述前加上对应的等级标签，如 `[核心不一致]` 或 `[次要不一致]`。
            *   对于所有识别出的不一致，请提供详细的修改建议。
            *   **特别注意：** 对于所有 `[次要不一致]`，请在修改建议后额外评估并指明：`[建议是否需要严格匹配prompt：是/否]`。如果选择"否"，请简要说明原因（例如：当前差异不影响核心传达，或允许一定灵活度）。

            **最终判断：**
            *   如果SVG代码和prompt经过全面检查后，**没有任何不一致**（无论是核心还是次要），请直接回复："**完全一致**"。
            *   否则，请严格按照上述要求，详细列出所有不一致之处。
        """
        
        self.ai2_system_prompt = """
            你是一个修改svg代码的高手。

            你的任务是根据reviewer的反馈，修改SVG代码使其与prompt完全一致。
            
            你将收到:
            1. 原始的SVG代码
            2. Reviewer指出的不一致之处和修改建议，其中不一致问题已分为 `[核心不一致]` 和 `[次要不一致]` 两个等级，并且对于 `[次要不一致]`，可能附带了"是否需要严格匹配prompt"的建议。

            **修改优先级与策略：**
            1.  **优先处理 `[核心不一致]`：** 务必确保所有被标记为 `[核心不一致]` 的问题得到完全且准确的修复，因为它们直接影响视频的核心信息传达和画面结构。
            2.  **处理 `[次要不一致]`：**
                *   如果reviewer明确指出 `[次要不一致]` 需要**严格匹配prompt**（即：`[建议是否需要严格匹配prompt：是]`），请将其视为必须修复的任务，并严格按照修改建议执行。
                *   如果reviewer指出 `[次要不一致]` **不需要严格匹配prompt**（即：`[建议是否需要严格匹配prompt：否]`），则表示当前差异在可接受范围内，**你无需对此处进行修改**。

            请仔细分析reviewer的反馈，并对SVG代码进行必要的修改，确保最终输出的SVG代码能够完全符合prompt的要求（根据reviewer的明确指示）。
            
            你的输出应该是完整的、可直接使用的SVG代码，它将被用来更新对应分镜的svg代码。
            
            在修改过程中，请确保：
            1.  **修复优先级：** 优先处理 `[核心不一致]`，确保核心内容和结构准确无误。
            2.  **全面实现：** 完整实现reviewer建议的所有需要修改之处（包括所有核心不一致，以及被要求严格匹配的次要不一致）。
            3.  **代码质量：** 保持SVG代码的结构清晰、可读性高。
            4.  **无新错误：** 不引入任何新的错误或问题。
            5.  **元素完整性：** 保留所有必要的SVG元素和属性。
            6.  **最终一致性：** 你的最终SVG代码应在所有被要求修复的方面与prompt达到完全一致。
        """

    async def process(self, state: Dict[str, Any]) -> Dict[str, Any]:
        """
        处理数据

        Args:
            state: 当前状态

        Returns:
            Dict[str, Any]: 更新后的状态
        """
        print("\n" + "="*50)
        print("【开始执行】SVG代码和prompt一致性检查 (SVGCodeChecker)")
        print("="*50)

        # 收集场景和镜头信息，准备批处理数据
        batch_items, news_report = self._collect_scenes_and_shots(state)
        
        # 执行并发处理对话
        results = await self._process_conversations(batch_items)
        
        # 更新状态和新闻报告
        self._update_state_with_results(state, results, news_report)

        # 从状态中获取项目信息
        project_dir = state["data"].get("project_dir", "")
        data_state_path = state["data"].get("data_state_path", "")
        scene_paths = state["data"].get("scene_paths", [])
        
        # 保存到数据库
        self._save_to_database(state, project_dir, data_state_path, scene_paths)

        print("="*50)
        print("【完成执行】SVG代码和prompt一致性检查 (SVGCodeChecker)")
        print("="*50 + "\n")
        
        return state
        
    def _collect_scenes_and_shots(self, state: Dict[str, Any]) -> Tuple[List[Tuple[str, Dict, str, str]], Dict[str, Any]]:
        """
        收集场景和镜头信息，准备批处理数据
        
        Args:
            state: 当前状态
            
        Returns:
            Tuple[List[Tuple[str, Dict, str, str]], Dict[str, Any]]: 批处理项列表和新闻报告
        """
        # 获取分析报告中的场景数据
        news_report = state["data"]["news_report"]
        sections = news_report.get("sections", [])
        print(f"从分析报告中获取到 {len(sections)} 个场景")

        # 准备批处理数据
        batch_items = []
        
        # 收集所有需要处理的场景和镜头
        shot_count = 0
        for i, section in enumerate(sections):
            section_id = f"section_{i+1}"
            
            # 获取当前场景的子镜头列表
            subsections = section.get("subsections", [])
            
            if subsections:
                # 如果有子镜头，则为每个子镜头生成对话
                for j, subsection in enumerate(subsections):
                    shot_count += 1
                    shot_id = f"{section_id}_shot_{j+1}"
                    script = subsection
                    # 获取SVG代码和prompt
                    svg_code = subsection.get("svg_code", "")
                    svg_prompt = subsection.get("svg_prompt", "")
                    if svg_code and svg_prompt:
                        batch_items.append((shot_id, script, svg_code, svg_prompt))
                        print(f"准备处理场景 {section_id} 的镜头 {j+1} 的SVG代码")
            else:
                # 如果没有子镜头，则为整个场景生成对话
                script = section
                # 获取SVG代码和prompt
                svg_code = section.get("svg_code", "")
                svg_prompt = section.get("prompt", "")
                if svg_code and svg_prompt:
                    batch_items.append((section_id, script, svg_code, svg_prompt))
                    print(f"准备处理场景 {i+1} 的SVG代码")

        print(f"总共准备处理 {len(batch_items)} 个场景/镜头的SVG代码")
        
        return batch_items, news_report
        
    async def _process_conversations(self, batch_items: List[Tuple[str, Dict, str, str]]) -> List[Tuple[str, List[Dict[str, str]], str]]:
        """
        执行并发处理对话
        
        Args:
            batch_items: 批处理项列表
            
        Returns:
            List[Tuple[str, List[Dict[str, str]], str]]: 处理结果列表，包含场景ID、对话记录和更新后的SVG代码
        """
        # 执行并发处理
        results = await asyncio.gather(*[self._process_single_conversation(item) for item in batch_items])
        return results
        
    async def _process_single_conversation(self, item: Tuple[str, Dict, str, str]) -> Tuple[str, List[Dict[str, str]], str]:
        """
        处理单个对话
        
        Args:
            item: 批处理项，包含场景ID、脚本、SVG代码和prompt
            
        Returns:
            Tuple[str, List[Dict[str, str]], str]: 场景ID、对话记录和更新后的SVG代码
        """
        shot_id, script, svg_code, svg_prompt = item
        print(f"开始处理 {shot_id} 的SVG代码检查")
        
        # 初始化AI对话
        conversation = AIConversation(
            ai1_name="SVG代码审查员",
            ai2_name="SVG代码修改师",
            ai1_character=self.ai1_system_prompt,
            ai2_character=self.ai2_system_prompt,
            script=script,
            max_rounds=5,
            ai2_temperature=0.0,  # 为SVG代码修改师设置temperature为0，使其输出更加确定性
            ai1_first=True  # 让SVG代码审查员先响应
        )
        
        # 准备初始消息
        initial_message = self._prepare_initial_message(svg_code, svg_prompt)
        
        # 开始对话
        conversation_log = await conversation.start_conversation(
            initial_message=initial_message,
            max_rounds=5,
            end_phrase="完全一致"
        )
        
        # 保存对话结果
        self._save_conversation_log(shot_id, conversation_log)
        
        # 从最后一条消息中提取更新后的SVG代码
        updated_svg_code = self._extract_updated_svg_code(conversation_log, svg_code)
        
        # 验证SVG代码是否有效
        is_valid, error_message = SVGUtils.validate_svg(updated_svg_code)
        
        if not is_valid:
            print(f"警告：{shot_id} 的更新SVG代码验证失败: {error_message}")
            # 如果更新的SVG代码无效，则尝试使用原始代码
            is_original_valid, original_error = SVGUtils.validate_svg(svg_code)
            if is_original_valid:
                print(f"使用原始SVG代码替代无效的更新代码")
                updated_svg_code = svg_code
            else:
                print(f"原始SVG代码也无效: {original_error}")
        
        return shot_id, conversation_log, updated_svg_code
        
    def _prepare_initial_message(self, svg_code: str, svg_prompt: str) -> str:
        """
        准备初始消息
        
        Args:
            svg_code: SVG代码
            svg_prompt: 提示词
            
        Returns:
            str: 初始消息
        """
        return f"""
            **Prompt**:
            {svg_prompt}

            **SVG代码**:
            {svg_code}
        """
                
    def _save_conversation_log(self, shot_id: str, conversation_log: List[Dict[str, str]]) -> None:
        """
        保存对话记录
        
        Args:
            shot_id: 场景ID
            conversation_log: 对话记录
        """
        conversation_file = f"svg_review_{shot_id}.json"
        conversation_path = os.path.join(self.conversation_dir, conversation_file)
        with open(conversation_path, "w", encoding="utf-8") as f:
            json.dump(conversation_log, f, ensure_ascii=False, indent=2)
        
        print(f"已保存 {shot_id} 的SVG代码检查结果到: {conversation_path}")
        
    def _extract_updated_svg_code(self, conversation_log: List[Dict[str, str]], original_svg_code: str) -> str:
        """
        从对话记录中提取更新后的SVG代码
        
        Args:
            conversation_log: 对话记录
            original_svg_code: 原始SVG代码
            
        Returns:
            str: 更新后的SVG代码
        """
        # 尝试从最后一条AI2的消息中提取SVG代码
        for message in reversed(conversation_log):
            if message.get("role") == "SVG代码修改师":
                content = message.get("content", "")
                # 使用SVGUtils提取SVG代码
                extracted_svg = SVGUtils.extract_svg_code(content)
                if extracted_svg:
                    return extracted_svg
        
        # 如果无法提取更新后的代码，返回原始代码
        print(f"警告：无法从对话中提取更新后的SVG代码，将使用原始代码")
        return original_svg_code
        
    def _update_state_with_results(self, state: Dict[str, Any], results: List[Tuple[str, List[Dict[str, str]], str]], 
                                 news_report: Dict[str, Any]) -> None:
        """
        更新状态和新闻报告
        
        Args:
            state: 当前状态
            results: 处理结果列表
            news_report: 新闻报告
        """
        # 获取场景列表
        sections = news_report.get("sections", [])
        
        # 更新新闻报告中的SVG代码和对话记录
        self._update_news_report_with_conversations(sections, results)
        
        # 更新news_report
        state["data"]["news_report"] = news_report
        
        # 更新当前步骤
        state["current_step"] = "svg_code_checker"
        
        # 保存完整的状态数据
        self.file_manager.write_json("state_data.json", state["data"])
        print(f"已保存带有SVG代码检查记录的状态数据到: state_data.json")
        
    def _update_news_report_with_conversations(self, sections: List[Dict[str, Any]], 
                                            results: List[Tuple[str, List[Dict[str, str]], str]]) -> None:
        """
        更新新闻报告中的SVG代码和对话记录
        
        Args:
            sections: 场景列表
            results: 处理结果列表
        """
        # 将列表转换为字典以便于查找
        result_dict = {shot_id: (log, updated_svg) for shot_id, log, updated_svg in results}
        
        for i, section in enumerate(sections):
            section_id = f"section_{i+1}"
            
            # 获取当前场景的子镜头列表
            subsections = section.get("subsections", [])
            
            if subsections:
                # 如果有子镜头，则更新每个子镜头的svg_code_review_conversation_log和svg_code
                for j, subsection in enumerate(subsections):
                    shot_id = f"{section_id}_shot_{j+1}"
                    if shot_id in result_dict:
                        # 更新子镜头的svg_code_review_conversation_log和svg_code
                        log, updated_svg = result_dict[shot_id]
                        sections[i]["subsections"][j]["svg_code_review_conversation_log"] = log
                        sections[i]["subsections"][j]["svg_code"] = updated_svg
                        print(f"已更新 {shot_id} 的svg_code和svg_code_review_conversation_log")
            else:
                # 如果没有子镜头，则更新整个场景的svg_code_review_conversation_log和svg_code
                if section_id in result_dict:
                    log, updated_svg = result_dict[section_id]
                    sections[i]["svg_code_review_conversation_log"] = log
                    sections[i]["svg_code"] = updated_svg
                    print(f"已更新 {section_id} 的svg_code和svg_code_review_conversation_log")

    def _save_to_database(self, state: Dict[str, Any], project_dir: str, data_state_path: str, scene_paths: List[Tuple[str, str]]) -> None:
        """
        将项目、场景和资产信息保存到数据库
        
        Args:
            state: 当前状态
            project_dir: 项目目录
            data_state_path: 数据状态路径
            scene_paths: 场景路径列表
        """
        try:
            print("\n===== 开始保存数据到数据库 =====")
            print(f"项目目录: {project_dir}")
            print(f"数据状态路径: {data_state_path}")
            print(f"场景路径数量: {len(scene_paths)}")

            # 获取热词和标题信息
            trend_word = state["data"].get("trend_word", "未知热词")
            title = state["data"].get("news_report", {}).get("title", "未知标题")
            s3_project_dir = state["data"].get("project_dir", "未获取到s3_project_dir")
            print(f"热词: {trend_word}, 标题: {title}")
            
            # 保存项目信息
            project_id = write_project_video_info(self.conf_path, trend_word, title, data_state_path)
            self.project_id = project_id
            print(f"已保存项目信息到数据库，项目ID: {project_id}")

            # 保存场景信息
            for i, (scene_id, s3_path) in enumerate(scene_paths):
                print(f"\n----- 处理场景 {i+1}/{len(scene_paths)}: {scene_id} -----")
                print(f"场景S3路径: {s3_path}")
                
                # 解析场景ID并保存场景信息
                scene_order, scene_title, scene_caption = self._parse_scene_info(state, scene_id)
                print(f"场景顺序: {scene_order}, 标题: {scene_title}")
                
                # 默认场景时长为5秒
                duration_seconds = 5000  # 毫秒

                # 保存场景信息 project_video_scene
                scene_id_db = write_project_video_scene(
                    self.conf_path,
                    project_id,
                    scene_order,
                    s3_path,
                    scene_caption,
                    data_state_path,  # 使用data_state_path替代未定义的script_path
                    duration_seconds,
                    scene_title
                )
                print(f"已保存场景 {scene_id} 信息到数据库，场景ID: {scene_id_db}")

                # 获取并保存资产信息
                try:
                    # 根据场景ID解析section和subsection索引
                    section_idx, subsection_idx = self._parse_scene_indices(scene_id)
                    print(f"从场景ID解析: 章节索引={section_idx}, 分镜索引={subsection_idx}")
                    
                    # 获取章节数据
                    sections = state["data"].get("news_report", {}).get("sections", [])
                    if section_idx < 0 or section_idx >= len(sections):
                        print(f"警告: 章节索引 {section_idx} 超出范围 (0-{len(sections)-1})")
                        continue
                    
                    section = sections[section_idx]
                    
                    # 如果有分镜索引，则获取对应的分镜数据
                    if subsection_idx is not None:
                        subsections = section.get("subsections", [])
                        if subsection_idx < 0 or subsection_idx >= len(subsections):
                            print(f"警告: 分镜索引 {subsection_idx} 超出范围 (0-{len(subsections)-1})")
                            continue
                        
                        subsection = subsections[subsection_idx]
                        print(f"开始保存分镜 {scene_id} 的资产到数据库...")
                        self.save_scene_assets_to_db(project_id, scene_order, subsection, s3_project_dir)
                    else:
                        # 如果没有分镜索引，则处理整个章节
                        print(f"开始保存章节 {scene_id} 的资产到数据库...")
                        self.save_scene_assets_to_db(project_id, scene_order, section, s3_project_dir)
                    
                except Exception as e:
                    print(f"保存场景 {scene_id} 资产到数据库失败: {e}")
                    import traceback
                    print(traceback.format_exc())

            # 将项目ID添加到状态中
            state["data"]["project_id"] = project_id
            state["data"]["project_dir"] = project_dir
            print("\n===== 完成保存数据到数据库 =====")

        except Exception as e:
            print(f"保存到数据库失败: {e}")
            import traceback
            print(traceback.format_exc())
            
    def _parse_scene_indices(self, scene_id: str) -> Tuple[int, Optional[int]]:
        """
        从场景ID解析章节和分镜索引
        
        Args:
            scene_id: 场景ID，格式为'section_X'或'section_X_shot_Y'
            
        Returns:
            Tuple[int, Optional[int]]: 章节索引和分镜索引的元组，如果没有分镜索引则为None
        """
        if "_shot_" in scene_id:
            # 镜头格式: section_X_shot_Y
            parts = scene_id.split('_')
            section_idx = int(parts[1]) - 1  # 转为0-based索引
            subsection_idx = int(parts[3]) - 1  # 转为0-based索引
            return section_idx, subsection_idx
        else:
            # 普通场景格式: section_X
            section_idx = int(scene_id.split('_')[1]) - 1  # 转为0-based索引
            return section_idx, None
            
    def save_scene_assets_to_db(self, project_id: int, scene_order: int, data: Dict[str, Any], s3_project_dir: str) -> None:
        """
        将场景或分镜中的资产保存到数据库

        Args:
            project_id: 项目ID
            scene_order: 场景顺序
            data: 场景或分镜数据
        """
        # 打印数据键
        print(f"数据键: {data.keys()}")
        
        # 检查assets字段是否存在
        if "assets" not in data:
            print(f"警告: 数据中没有assets字段")
            return
            
        assets = data.get("assets", {})
        print(f"资产数据类型: {type(assets)}")
        
        if not isinstance(assets, dict):
            print(f"警告: 资产数据不是字典类型: {type(assets)}")
            return
            
        print(f"资产数据键: {assets.keys()}")

        # 处理人物资产
        character_assets = assets.get("characterAssets", [])
        print(f"人物资产数量: {len(character_assets)}")
        for i, asset in enumerate(character_assets):
            print(f"\n处理人物资产 {i+1}/{len(character_assets)}")
            self._save_asset_to_db(project_id, scene_order, asset, s3_project_dir)

        # 处理道具资产
        prop_assets = assets.get("propAssets", [])
        print(f"道具资产数量: {len(prop_assets)}")
        for i, asset in enumerate(prop_assets):
            print(f"\n处理道具资产 {i+1}/{len(prop_assets)}")
            self._save_asset_to_db(project_id, scene_order, asset, s3_project_dir)
            
    def _save_asset_to_db(self, project_id: int, scene_order: int, asset: Dict[str, Any], s3_project_dir: str) -> None:
        """
        将单个资产保存到数据库

        Args:
            project_id: 项目ID
            scene_order: 场景顺序
            asset: 资产数据
        """
        try:
            # 获取资产信息
            name = asset.get("name", "未命名资产")
            path = asset.get("path", "")
            material_type = asset.get("image_type", "").lower()
            material_path = os.path.join(s3_project_dir, path)
            print(f"资产名称: {name}")
            print(f"资产路径: {material_path}")
            print(f"资产类型: {material_type}")
            
            
            # 如果路径仍为空，则跳过
            if not material_path:
                print(f"资产 {name} 的路径无效: {material_path}")
                return

            # 配置JSON，包含资产的描述和宽高比
            config = {
                "description": asset.get("description", ""),
                "aspectRatio": asset.get("aspectRatio", "1:1")
            }
            config_json = str(config)
            print(f"资产配置: {config_json}")
            
            # 将资产信息添加到数据库
            material_id = write_project_video_scene_material(
                self.conf_path,
                project_id,
                scene_order,
                material_type,
                material_path,
                name,
                config_json
            )
            print(f"已保存资产 {name} 的信息到数据库，材料ID: {material_id}")
        except Exception as e:
            print(f"保存资产 {name} 到数据库失败: {e}")
            import traceback
            print(traceback.format_exc())
            
    def _parse_scene_info(self, state: Dict[str, Any], scene_id: str) -> Tuple[int, str, str]:
        """
        解析场景ID并获取场景信息
        
        Args:
            state: 当前状态
            scene_id: 场景ID
            
        Returns:
            Tuple[int, str, str]: 包含(场景顺序, 场景标题, 场景描述)的元组
        """
        # 解析场景ID，处理可能的镜头ID格式 (section_X_shot_Y)
        if "_shot_" in scene_id:
            # 镜头格式: section_X_shot_Y
            parts = scene_id.split('_')
            section_num = int(parts[1])
            shot_num = int(parts[3])
            scene_order = (section_num - 1) * 10 + shot_num  # 为每个场景预留10个镜头位置
            
            # 获取对应的场景和镜头标题
            section = state["data"].get("news_report", {}).get("sections", [])[section_num-1]
            if "subsections" in section and len(section["subsections"]) >= shot_num:
                scene_caption = section["subsections"][shot_num-1].get("content", f"场景{section_num}镜头{shot_num}内容")
                scene_title = section["subsections"][shot_num-1].get("title", f"场景{section_num}镜头{shot_num}标题")
            else:
                scene_caption = f"场景{section_num}镜头{shot_num}内容"
                scene_title = f"场景{section_num}镜头{shot_num}标题"
        else:
            # 普通场景格式: section_X
            scene_order = int(scene_id.split('_')[1])
            scene_caption = state["data"].get("news_report", {}).get("sections", [])[scene_order-1].get("content", f"场景{scene_order}内容")
            scene_title = state["data"].get("news_report", {}).get("sections", [])[scene_order-1].get("title", f"场景{scene_order}标题")
            
        return scene_order, scene_title, scene_caption