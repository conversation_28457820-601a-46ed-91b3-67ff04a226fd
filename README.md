

# 技术选型
使用python，langgraph 库
python 环境使用项目目录下的 venv,激活环境：.\venv\Scripts\activate.bat
使用 langgraph 工作流图来实现这个项目，创建 8 个node节点，分别就是下面八个业务方法的调用

定义一个 .env 文件存放的就是各种的配置文件，claude api_key，等等

# 业务方法
1. 处理 data.json 的数据，提取出来 hot_word 和 zhisou_content 到一个新的文件 extracted_data.json。只需要定义在一个新的文件定义类和方法，不需要实现.
2. 创建 一个 prompt 目录，下面存放的是这个项目下面用到的所有 prompt,用到的prompt有：0.analyze_5W1Hmd，1.news_report_prompt.md，2.scene_2_scene_asset.md，4.generate_svg_llm_prompt.md，5.generate_scene_svg_prompt.md
3. 创建一个 llm 目录，下面新建一个claude.py 文件，里面有一个claudeprovider 类，下面实现了一个 async def generate_response(self, messages: List[Message]) 方法，每次调用generate_response之前，需要先组装 messages（1. 获取对应的prompt提示词， 2. 拼接历史消息，3. 追加用户的最新的query ）
4. 调用 claude 模型抽取数据，输入 extracted_data.json + 0.analyze_5W1H.md，输出 analysis.json。实现这个类，只需要定义在一个新的文件定义类和方法，不需要实现.
5. 调用 claude 模型生成json格式的报告，输入 analysis.json + 1.news_report_prompt.md，输出 news_reports.json。实现这个类，只需要定义在一个新的文件定义类和方法，不需要实现.
6. 调用 claude 模型分析场次里面的资产，输入 news_reports.json + 2.scene_2_scene_asset.md，输出 scenes_asset.json。实现这个类，只需要定义在一个新的文件定义类和方法，不需要实现.
7. 调用 claude 模型生成 svg 动画提示词，输入 scenes_asset.json + 4.generate_svg_llm_prompt.md，遍历生成每个场次的 svg 动画提示词，输出 generate_scene_svg_prompt_xxx_xxx.md（创建一个 generate_scene_svg_prompt_xxx 的目录）。实现这个类，只需要定义在一个新的文件定义类和方法，不需要实现.
8. 调用 claude 模型生成 svg 动画，输入 5.generate_scene_svg_prompt_xxx_xxx.md，输出 svg 动画文件。实现这个类，只需要定义在一个新的文件定义类和方法，不需要实现.




prompt 换到user 里面


json解析应该优化一下

查看：file:///C:/Users/<USER>/Downloads/data/news_report.json 的提示词是否优化的可以用了
（可以用了）
下一步优化生成svg的提示词（采用分步骤，然后嵌入到提示词中）



修复json解析失败 ""

新增一个代码检查，svg代码是否按照预期来的


news_report 应该添加 更多的分镜视频的背景信息？



粘贴生成的html代码？
不可以用的话调试提示词



检查html代码和动画设计文稿是否一致

调整讨厌和剪辑师的讨论，导演设计必须可用

