import os
import json
import re
import json_repair
from typing import Any, Dict, Optional

HAS_JSON_REPAIR = True

class FileManager:
    """文件管理器"""

    def __init__(self, data_dir: str = "data"):
        """
        初始化文件管理器

        Args:
            data_dir: 数据目录
        """
        self.data_dir = data_dir
        os.makedirs(data_dir, exist_ok=True)

    def read_json(self, filename: str) -> Dict[str, Any]:
        """
        读取JSON文件

        Args:
            filename: 文件名

        Returns:
            Dict[str, Any]: JSON数据
        """
        filepath = os.path.join(self.data_dir, filename)
        with open(filepath, "r", encoding="utf-8") as f:
            return json.load(f)

    def write_json(self, filename: str, data: Dict[str, Any]):
        """
        写入JSON文件

        Args:
            filename: 文件名
            data: 要写入的数据
        """
        # 确保目录存在
        os.makedirs(os.path.dirname(os.path.join(self.data_dir, filename)), exist_ok=True)
        filepath = os.path.join(self.data_dir, filename)
        with open(filepath, "w", encoding="utf-8") as f:
            json.dump(data, f, ensure_ascii=False, indent=2)

    def read_text(self, filename: str) -> str:
        """
        读取文本文件

        Args:
            filename: 文件名

        Returns:
            str: 文件内容
        """
        filepath = os.path.join(self.data_dir, filename)
        with open(filepath, "r", encoding="utf-8") as f:
            return f.read()

    def write_text(self, filename: str, content: str):
        """
        写入文本文件

        Args:
            filename: 文件名
            content: 要写入的内容
        """
        filepath = os.path.join(self.data_dir, filename)
        with open(filepath, "w", encoding="utf-8") as f:
            f.write(content)

def safe_parse_json(json_str: str, default_value: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
    """
    安全地解析JSON字符串，处理常见的格式问题
    
    使用json_repair库来修复和解析不标准的JSON字符串

    Args:
        json_str: JSON字符串
        default_value: 解析失败时返回的默认值

    Returns:
        Dict[str, Any]: 解析后的JSON数据
    """
    if default_value is None:
        default_value = {}

    if not json_str or not json_str.strip():
        print("JSON字符串为空")
        return default_value

    # 预处理：清理字符串
    try:
        # 移除Markdown代码块标记
        json_str = json_str.strip()
        if json_str.startswith("```json") or json_str.startswith("```"):
            # 移除开头的```json或```
            json_str = re.sub(r'^```(?:json)?\s*', '', json_str)
            # 移除结尾的```
            json_str = re.sub(r'\s*```$', '', json_str)
            json_str = json_str.strip()

        # 如果清理后字符串为空，返回默认值
        if not json_str:
            print("清理后JSON字符串为空")
            return default_value

        # 使用json_repair库修复和解析JSON
        if HAS_JSON_REPAIR:
            try:
                # 尝试直接使用json_repair.loads
                result = json_repair.loads(json_str)
                print("已成功使用json_repair解析JSON响应")
                return result
            except Exception as e:
                print(f"json_repair解析错误: {e}")
                # 如果json_repair失败，尝试先修复再解析
                try:
                    repaired_json = json_repair.repair_json(json_str, ensure_ascii=False)
                    if repaired_json:
                        result = json.loads(repaired_json)
                        print("已成功使用json_repair修复并解析JSON响应")
                        return result
                    else:
                        print("json_repair修复后返回空字符串")
                        return default_value
                except Exception as e2:
                    print(f"json_repair修复失败: {e2}")
                    # 尝试使用备用方法
        
        # 备用方法：使用原始实现
        # 移除所有控制字符
        json_str = ''.join(ch if ord(ch) >= 32 or ch == '\n' else ' ' for ch in json_str)

        # 规范化换行符
        json_str = json_str.replace('\r\n', '\n').replace('\r', '\n')

        # 处理未转义的引号
        json_str = _fix_escape_chars(json_str)

        # 尝试解析JSON
        try:
            parsed = json.loads(json_str)
            print("已成功解析JSON响应")
            return parsed
        except json.JSONDecodeError as e:
            print(f"JSON解析错误: {e}")
            # 尝试清理和重新解析
            cleaner_str = re.sub(r'[\n\t\r]+', ' ', json_str)
            cleaner_str = re.sub(r'\s+', ' ', cleaner_str).strip()
            
            try:
                parsed = json.loads(cleaner_str)
                print("已成功解析清理后的JSON响应")
                return parsed
            except json.JSONDecodeError as e:
                print(f"清理后JSON解析仍然失败: {e}")
                return default_value

    except Exception as e:
        print(f"处理JSON字符串时出错: {e}")
        return default_value

def _fix_escape_chars(json_str: str) -> str:
    """
    修复JSON字符串中的转义字符问题

    Args:
        json_str: 需要修复的JSON字符串

    Returns:
        str: 修复后的JSON字符串
    """
    # 处理字符串中的未转义引号
    result = ""
    in_string = False
    i = 0

    while i < len(json_str):
        char = json_str[i]
        
        # 处理字符串开始和结束
        if char == '"' and (i == 0 or json_str[i-1] != '\\'):
            in_string = not in_string
            result += char
        # 在字符串内部，处理未转义的引号
        elif in_string and char == '"' and i > 0 and json_str[i-1] != '\\':
            result += '\\"'  # 添加转义
        # 处理其他字符
        else:
            result += char

        i += 1

    # 处理特殊字符
    pattern = r'("(?:[^"\\]|\\.)*")'
    
    def escape_special_chars(match):
        s = match.group(1)
        # 确保字符串中的特殊字符都被正确转义
        s = s.replace('\\', '\\\\')  # 转义反斜杠
        s = s.replace('\n', '\\n')   # 转义换行符
        s = s.replace('\t', '\\t')   # 转义制表符
        s = s.replace('\r', '\\r')   # 转义回车符
        return s

    result = re.sub(pattern, escape_special_chars, result)
    
    return result