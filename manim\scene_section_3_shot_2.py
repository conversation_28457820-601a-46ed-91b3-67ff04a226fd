from manim import *
import os
from layout_manager import LayoutManager

'''
========== 动画素材清单 ==========

## 图片素材：
- 新闻图标: 新闻图标.png - 新闻通报或官方公告的象征性图标
- 调查图标: 调查图标.png - 代表调查或法律程序的图标
- 对比背景: 对比背景.jpg - 左右分割的背景图片，左侧蓝色调，右侧黄/橙色调
- 问号背景: 问号背景.png - 带有问号图案的半透明背景

## 文字关键词素材：
- "雅安市联合工作组调查通报" - 0.0s-3.0s - 黑体粗体
- "2023年5月22日" - 0.5s-3.0s - 细黑体
- "违规经商办企业" - 4.0s-7.0s - 黑体
- "隐瞒违法生育二孩" - 4.0s-7.0s - 黑体
- "已被立案调查" - 8.0s-10.0s - 黑体加粗红色
- "然而" - 10.0s-11.0s - 黑体加粗
- "官方已回应" - 11.0s-16.0s - 蓝色黑体
- "- 违规经商办企业\n- 隐瞒违法生育" - 11.0s-16.0s - 蓝色黑体
- "未回应核心质疑" - 11.0s-16.0s - 橙黄色黑体加粗
- "耳环真伪" - 14.0s-16.0s - 黄色黑体
- "家庭巨额财富来源" - 14.5s-16.0s - 黄色黑体
- "网友：为何对最关键问题避而不谈？" - 15.0s-16.0s - 白色黑体斜体

## 动画总时长：16秒
=====================================
'''

class YaanInvestigationReport(Scene):
    def construct(self):
        # 设置背景色
        self.camera.background_color = WHITE
        
        # 检查资源文件是否存在
        def check_file_exists(file_path):
            if not os.path.exists(file_path):
                print(f"警告: 文件 {file_path} 不存在!")
                return False
            return True
        
        # 资源路径
        news_icon_path = "新闻图标.png"
        investigation_icon_path = "调查图标.png"
        comparison_bg_path = "对比背景.jpg"
        question_bg_path = "问号背景.png"
        
        # 检查资源文件
        check_file_exists(news_icon_path)
        check_file_exists(investigation_icon_path)
        check_file_exists(comparison_bg_path)
        check_file_exists(question_bg_path)
        
        # 第一阶段 (0.0s-3.0s): 通报开场
        # 创建新闻图标
        news_icon = ImageMobject(news_icon_path) if check_file_exists(news_icon_path) else Square(color=BLUE, fill_opacity=0.5)
        news_icon.height = 1
        news_icon.width = 1  # 宽高比1:1
        news_icon.move_to([0, 1.5, 0])
        news_icon.set_opacity(0)
        
        # 创建标题文本
        title = Text("雅安市联合工作组调查通报", font="SimHei", weight=BOLD, color=BLACK, font_size=36)
        title.set_opacity(0)
        
        # 创建日期文本
        date = Text("2023年5月22日", font="SimHei", color=BLACK, font_size=24)
        date.move_to([0, -1, 0])
        date.set_opacity(0)
        
        # 添加元素到场景并执行动画
        self.add(news_icon, title, date)
        
        # 新闻图标从上方滑入
        self.play(
            news_icon.animate.set_opacity(1).shift(DOWN * 1),
            run_time=0.8
        )
        
        # 标题下拉展开效果
        self.play(
            title.animate.set_opacity(1),
            run_time=0.7
        )
        
        # 日期淡入
        self.play(
            date.animate.set_opacity(1),
            run_time=0.5
        )
        
        # 保持画面显示一段时间
        self.wait(1)
        
        # 第二阶段 (4.0s-7.0s): 违规内容展示
        # 创建违规经商文本
        business_violation = Text("违规经商办企业", font="SimHei", color=BLACK, font_size=30)
        business_violation.move_to([-3.5, 0, 0])
        business_violation.set_opacity(0)
        
        # 创建隐瞒生育文本
        birth_violation = Text("隐瞒违法生育二孩", font="SimHei", color=BLACK, font_size=30)
        birth_violation.move_to([3.5, 0, 0])
        birth_violation.set_opacity(0)
        
        # 移动新闻图标到左侧
        self.play(
            news_icon.animate.move_to([-3.5, 1.5, 0]),
            FadeOut(title),
            FadeOut(date),
            run_time=1
        )
        
        # 显示违规内容
        self.play(
            business_violation.animate.set_opacity(1).scale(1.2),
            run_time=0.8
        )
        self.play(
            business_violation.animate.scale(1/1.2),
            run_time=0.2
        )
        
        self.play(
            birth_violation.animate.set_opacity(1).scale(1.2),
            run_time=0.8
        )
        self.play(
            birth_violation.animate.scale(1/1.2),
            run_time=0.2
        )
        
        # 保持画面显示
        self.wait(1)
        
        # 第三阶段 (8.0s-10.0s): 立案调查
        # 创建调查图标
        investigation_icon = ImageMobject(investigation_icon_path) if check_file_exists(investigation_icon_path) else Square(color=RED, fill_opacity=0.5)
        investigation_icon.height = 1.5
        investigation_icon.width = 1.5  # 宽高比1:1
        investigation_icon.move_to([0, -1, 0])
        investigation_icon.set_opacity(0)
        
        # 创建立案调查文本
        investigation_text = Text("已被立案调查", font="SimHei", weight=BOLD, color=RED, font_size=36)
        investigation_text.move_to([0, 0.5, 0])
        investigation_text.set_opacity(0)
        investigation_text.scale(0.5)
        
        # 淡出前一阶段元素
        self.play(
            FadeOut(news_icon),
            FadeOut(business_violation),
            FadeOut(birth_violation),
            run_time=0.5
        )
        
        # 显示立案调查文本和图标
        self.play(
            investigation_text.animate.set_opacity(1).scale(2),
            run_time=1
        )
        
        self.play(
            investigation_icon.animate.set_opacity(1).rotate(2*PI),
            run_time=1
        )
        
        # 保持画面显示
        self.wait(0.5)
        
        # 第四阶段 (10.0s-11.0s): 转折点
        # 创建"然而"文本
        however_text = Text("然而", font="SimHei", weight=BOLD, color=BLACK, font_size=36)
        however_text.set_opacity(0)
        
        # 加载对比背景
        comparison_bg = ImageMobject(comparison_bg_path) if check_file_exists(comparison_bg_path) else Rectangle(width=14, height=8, color=BLUE, fill_opacity=0.3)
        comparison_bg.height = 8
        comparison_bg.width = 14  # 宽高比16:9
        comparison_bg.set_opacity(0)
        
        # 淡出前一阶段元素
        self.play(
            FadeOut(investigation_text),
            FadeOut(investigation_icon),
            run_time=0.5
        )
        
        # 显示"然而"文本
        self.play(
            however_text.animate.set_opacity(1).scale(1.5),
            run_time=0.5
        )
        
        # 保持画面显示
        self.wait(0.5)
        
        # 第五阶段 (11.0s-13.0s): 对比展示开始
        # 创建对比背景
        self.play(
            FadeOut(however_text),
            comparison_bg.animate.set_opacity(0.8),
            run_time=0.5
        )
        
        # 创建左侧标题
        left_title = Text("官方已回应", font="SimHei", color=BLUE, font_size=28)
        left_title.move_to([-5, 2.5, 0])
        left_title.set_opacity(0)
        
        # 创建左侧内容
        left_content = Text("- 违规经商办企业\n- 隐瞒违法生育", font="SimHei", color=BLUE, font_size=20)
        left_content.move_to([-5, 0, 0])
        left_content.set_opacity(0)
        
        # 创建右侧标题
        right_title = Text("未回应核心质疑", font="SimHei", weight=BOLD, color="#FFA500", font_size=28)  # 橙黄色
        right_title.move_to([5, 2.5, 0])
        right_title.set_opacity(0)
        
        # 加载问号背景
        question_bg = ImageMobject(question_bg_path) if check_file_exists(question_bg_path) else Rectangle(width=7, height=8, color=YELLOW, fill_opacity=0.2)
        question_bg.height = 8
        question_bg.width = 7  # 宽高比16:9
        question_bg.move_to([3.5, 0, 0])
        question_bg.set_opacity(0)
        
        # 显示左侧标题和内容
        self.play(
            left_title.animate.set_opacity(1),
            run_time=0.5
        )
        
        self.play(
            left_content.animate.set_opacity(1),
            run_time=0.5
        )
        
        # 显示右侧标题和问号背景
        self.play(
            right_title.animate.set_opacity(1),
            question_bg.animate.set_opacity(0.3),
            run_time=0.5
        )
        
        # 保持画面显示
        self.wait(0.5)
        
        # 第六阶段 (14.0s-16.0s): 未回应的核心质疑
        # 创建右侧内容
        earring_question = Text("耳环真伪", font="SimHei", color=YELLOW, font_size=28)
        earring_question.move_to([5, 1, 0])
        earring_question.set_opacity(0)
        
        wealth_question = Text("家庭巨额财富来源", font="SimHei", color=YELLOW, font_size=28)
        wealth_question.move_to([5, -0.5, 0])
        wealth_question.set_opacity(0)
        
        # 确保不重叠
        safe_pos = LayoutManager.get_safe_position_below(earring_question, wealth_question, margin=1.0)
        wealth_question.move_to(safe_pos)
        
        # 创建网友评论
        comment = Text("网友：为何对最关键问题避而不谈？", font="SimHei", slant=ITALIC, color=WHITE, font_size=18)
        comment.move_to([4, -2.5, 0])
        comment.set_opacity(0)
        
        # 显示右侧内容
        self.play(
            earring_question.animate.set_opacity(1).scale(1.2),
            run_time=0.5
        )
        self.play(
            earring_question.animate.scale(1/1.2),
            run_time=0.2
        )
        
        self.play(
            wealth_question.animate.set_opacity(1).scale(1.2),
            run_time=0.5
        )
        self.play(
            wealth_question.animate.scale(1/1.2),
            run_time=0.2
        )
        
        # 显示网友评论
        self.play(
            comment.animate.set_opacity(1),
            run_time=0.5
        )
        
        # 右侧区域闪烁效果
        self.play(
            question_bg.animate.set_opacity(0.5),
            run_time=0.25
        )
        self.play(
            question_bg.animate.set_opacity(0.3),
            run_time=0.25
        )
        
        # 保持最终画面显示
        self.wait(0.1)