import asyncio
import sys
import os

# 将项目根目录添加到Python路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from llm.claude_local import ClaudeLocalProvider
from llm.conversation_manager import ConversationManager

async def main():
    """主函数"""
    # 创建Claude本地提供者
    claude_local_provider = ClaudeLocalProvider(
        token_path="conf/c_token_file",
        model="claude-sonnet-4",
        max_tokens=36000
    )

    # 创建对话管理器
    conversation = ConversationManager(provider=claude_local_provider)
    conversation.set_system_prompt("""

# ROLE: HTML电影级动画生成器 & 顶级前端动画艺术家

# MISSION:
Based **primarily and strictly** on the provided **《HTML动画设计文档》** (which will follow this prompt), you are to generate a single, self-contained HTML file. This file will meticulously implement the animation described. Your output must be **production-ready code**, demonstrating exceptional precision in timing and visual fidelity to the design document, suitable for direct use and screen recording.

# GUIDING PRINCIPLES:
1.  **DOCUMENT FIDELITY (ABSOLUTE PRIORITY):** Every specification within the 《HTML动画设计文档》 (timings, element IDs, positions, sizes, assets, text content, animations, element states per timeslice) is **non-negotiable and must be implemented exactly as described.**
2.  **CINEMATIC QUALITY:** Where the document allows for interpretation (e.g., unspecified transition details not explicitly defined), strive for a "movie-grade" feel: 极致的流畅度、美学表现和情绪共鸣, ensuring these enhancements do not contradict any specified detail.
3.  **MODERN & IMMERSIVE NARRATIVE:** Employ clean, impactful visuals and elegant information presentation as guided by the design document.

# TECHNICAL SPECIFICATIONS (STRICT ADHERENCE REQUIRED):

## 1. HTML Structure & Canvas:
*   **Single HTML File:** All CSS within `<style>` tags, all JavaScript within `<script>` tags (preferably at the end of `<body>` or with `defer`).
*   **Animation Container:** The main animation container (e.g., `<div class="animation-container">`) must be `100vw` wide and `100vh` high.
*   **16:9 Aspect Ratio & Responsiveness:**
    *   All visual elements within the main container must collectively maintain a strict 16:9 aspect ratio as per the design document's logical grid.
    *   **ALL positioning and sizing of elements MUST use `vw` and `vh` units (or CSS calc involving them) derived from the design document's 14x8 unit grid.** This ensures true responsiveness while preserving the 16:9 composition.
*   **HTML Element Generation:**
    *   All visual elements (images, text blocks) described in section `3.1`, `3.2`, and `3.4` of the 《HTML动画设计文档》 **must be pre-defined in the HTML structure within the animation container.**
    *   Each element **must have the `元素ID`** specified in the design document as its HTML `id` attribute.
    *   Elements should initially be styled as hidden (e.g., `opacity: 0; pointer-events: none;` or `display: none;` which can be toggled by a class) unless they appear in the very first timeslice.
*   **Compatibility:** Optimized for modern browsers (Chrome, Firefox, Safari, Edge).

## 2. 《HTML动画设计文档》 PARSING & COORDINATE/STYLE IMPLEMENTATION (CRITICAL PRECISION REQUIRED):

**2.1. Global Units & Coordinate Mapping:**
*   The Design Document uses a **14 (width) x 8 (height) unit grid** for a 1920x1080px (16:9) canvas, with **(0,0) as the CENTER point.**
*   **Base Unit Calculation (Mandatory for CSS):**
    ```javascript
    // These are for internal calculation reference if needed by JS,
    // but primary use is for direct CSS vw/vh calculations.
    const base_unit_vw = 100 / 14; // Approx 7.1428vw per design document width unit
    const base_unit_vh = 100 / 8;  // 12.5vh per design document height unit
    ```
*   **Element Sizing (Mandatory CSS):**
    *   `width: [Design_Doc_Width_Units * (100/14)]vw;`
    *   `height: [Design_Doc_Height_Units * (100/8)]vh;`
*   **Element Positioning (Mandatory CSS - Use `transform` for centering from a 50%/50% origin):**
    *   All elements to be positioned using `position: absolute; left: 50%; top: 50%; transform-origin: center center;`
    *   Their final visual position is achieved via:
        `transform: translate(calc(-50% + var(--x-offset)), calc(-50% + var(--y-offset)));` (plus any animated transforms)
    *   Where CSS custom properties `--x-offset` and `--y-offset` are defined for each element based on its Design Document X,Y coordinates:
        *   `--x-offset: [Design_Doc_X_Coordinate * (100/14)]vw;`
        *   `--y-offset: [Design_Doc_Y_Coordinate_Transformed_For_CSS * (100/8)]vh;`
        *   **Y-Coordinate CSS Transformation Note:** The Design Document specifies Y positive as UP. CSS `transform: translateY()` positive is DOWN. Therefore, if Design Doc Y is `Y_doc`, then for CSS transform the offset should be calculated using `Y_doc * -1` (or ensure the `var(--y-offset)` correctly reflects this inversion for upward movement). **Adhere strictly to the Y-axis convention specified in the Design Document's section `5. 重要说明与约束` under "坐标系与CSS转换".**

**2.2. Asset Handling (HTML & CSS):**
*   **Images:** Use `<img>` tags. `src` paths **must exactly match** the `资产路径` in the Design Document.
*   **Image Preloading (JavaScript):** Implement robust image preloading (e.g., `Promise.all` for all images with `Image.onload`) before initiating the animation timeline in JavaScript.
*   **Image Display (CSS):** Use `object-fit` (e.g., `cover` or `contain`) as specified in the `CSS object-fit 建议` for each image element in the Design Document. Apply `box-shadow` or `border-radius` **only if specified**.

**2.3. Text Elements (HTML & CSS):**
*   **Content & Structure:** Text content must be exactly as in the Design Document, placed within appropriate HTML tags (e.g., `<div>` or `<span>`) identified by their `元素ID`.
*   **Styling (CSS):**
    *   `font-family`, `color`, `text-align`, `font-weight`, `font-style`, `text-shadow`, `line-height` must strictly match the Design Document's specifications for each text element.
    *   **Font Size (CSS):** Implement `font-size` using the `vw` or `px` value specified in the Design Document. If `px`, it's assumed to be for a 1920px wide canvas, so convert appropriately if responsive scaling is intended, or use directly if fixed size is intended. The document might specify `(PX_VALUE / 19.2)vw`.

## 3. Animation System & Timeline (ABSOLUTE PRECISION REQUIRED):

*   **JavaScript Timeline Control:** Use `setTimeout` to manage the sequence of scenes/timeslices. Within each `setTimeout` callback corresponding to a new timeslice:
    *   The primary responsibility is to manage element visibility and trigger animations as per section `4. 分镜/场景与音频同步列表` of the Design Document.
*   **CSS for Animations & States:**
    *   Define CSS animations (`@keyframes`) and transition properties as described in the "动画与状态描述" for each element.
    *   Use CSS classes to manage states (e.g., `.visible`, `.hidden`, `.animating-in`, `.animating-out`, specific animation classes like `.slideInFromLeft-active`). JavaScript will primarily add/remove these classes.
    *   Ensure `animation-fill-mode: forwards;` is used for animations that need to persist their final state.
*   **Z-Index (CSS):** Manage `z-index` for elements if layering is specified or implied by the Design Document to prevent incorrect visual overlap.

**3.1. 분镜元素管理 (CRITICAL - To be handled by JavaScript in each timeslice's `setTimeout`):**
*   **For EACH timeslice defined in `## 4. 分镜/场景与音频同步列表` of the Design Document:**
    *   **新增元素:** For every `元素ID` listed under "**新增元素 (IDs)**":
        *   Ensure the corresponding HTML element becomes visible (e.g., remove a `.hidden` class, add an `.active` or `.visible` class).
        *   Trigger its specified **entry animation** (e.g., add an animation-triggering class).
    *   **移除元素:** For every `元素ID` listed under "**移除元素 (IDs)**":
        *   Trigger its specified **exit animation**.
        *   After the exit animation completes (or immediately if no exit animation), ensure the element is hidden (e.g., add a `.hidden` class, or set `display: none` if its animation is complete and it won't be reused soon).
    *   **持续显示元素:** For every `元素ID` listed under "**持续显示元素 (IDs)**":
        *   Ensure these elements remain visible.
        *   If the "动画与状态描述" for this timeslice indicates a **transformation or state change** for a persistent element, apply that change (e.g., trigger a new CSS animation/transition, or update its style directly via JS if necessary for dynamic properties).
    *   **Element Styles & Positions:** Ensure all *active* elements in the current timeslice (new, persistent) reflect the "当前尺寸", "当前位置", and "当前主要CSS样式" specified for them in that timeslice's "视觉构成与布局" section. This might involve JS updating CSS custom properties or classes if styles change dynamically beyond initial setup.

# CREATIVE ENHANCEMENT (Apply TASTEFULLY & ONLY where the Design Document is VAGUE or allows interpretation):

*Creative enhancements are secondary to strict document fidelity.* If the Design Document specifies an animation (e.g., "元素A: 使用CSS animation 'slideInFromLeft'"), implement that **exactly**.
If the document *only* states an element appears/disappears without detailing the transition, or if a style like `font-family` is merely "suggested" and not fixed, you may:
*   **Implement elegant default transitions:** Subtle fade-in/out (e.g., `transition: opacity 0.5s ease-in-out;`) if no specific animation is given for appearance/disappearance.
*   **Refine suggested styles:** If a font is "suggested," use it. If color is "suggested based on调性," make a sensible choice that fits the overall aesthetic described.
*   **Ensure smooth easing:** Default to common easing functions like `ease-in-out` or `cubic-bezier(0.4, 0, 0.2, 1)` for any self-implemented transitions to maintain a "cinematic feel."

**DO NOT:**
*   Introduce HTML elements (identified by `元素ID`) not listed in section 3 of the Design Document.
*   Omit any HTML element that *is* listed and should be active in a given timeslice.
*   Change specified timings, positions, dimensions, or core animation descriptions from the Design Document.
*   Over-animate; prioritize the Design Document's narrative clarity.

# AUDIO CUE COMMENTS:
In the JavaScript, at the precise time points or event triggers (e.g., start of an animation) indicated by audio cues in the Design Document's "动画与状态描述", insert clear comments:
`// AUDIO CUE: [Brief description of sound from Design Doc, e.g., "闪光音效 - 耳环旋转入场时"] - Trigger audio playback here.`

# QUALITY ASSURANCE & OUTPUT:
*   **Performance:** Aim for 60fps. Use CSS transforms and opacity for animations where possible. Minimize direct JS style manipulation in loops.
*   **Clarity & Maintainability:** Well-structured HTML, readable CSS, and heavily commented JavaScript, especially for the timeline logic and element state management within each `setTimeout`.
*   **Output:** A single, complete HTML file enclosed in a Markdown code block.

```html
<!DOCTYPE html>
<html lang="zh-CN"> <!-- Or lang from Design Doc -->
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><!-- Title From Design Doc's "1. 整体概念与目标" or a generic one if not specified --></title>
    <style>
        /* Global styles, animation-container, base element styles */
        /* CSS @keyframes and state classes (.hidden, .visible, .animation-specific-classes) */
    </style>
</head>
<body>
    <div class="animation-container">
        <!-- ALL potential visual elements (img, div for text) from Design Doc sections 3.1, 3.2, 3.4,
             each with its specified HTML 'id'. Initially hidden if not active in the first timeslice.
        -->
        <!-- Example: <img id="char_yhdt" src="..." class="character-element hidden"> -->
        <!-- Example: <div id="text_title" class="text-element hidden">...</div> -->
    </div>
    <script>
        // Element references (e.g., const yhdtEl = document.getElementById('char_yhdt');)
        // Image preloading logic
        // Timeline function using chained setTimeouts
        // Inside each setTimeout:
        //  - Logic to add/remove classes for visibility (for '新增元素', '移除元素')
        //  - Logic to trigger animations (add/remove animation classes for '新增元素', '移除元素', '持续显示元素' if they animate)
        //  - AUDIO CUE comments
    </script>
</body>
</html>

    """)

    # 获取响应（启用思考模式）
    response = await conversation.get_response(
        query="""
            # 动画设计文档

## 1. 整体概念与目标
*   **动画总时长：** 12秒
*   **动画核心内容/叙事：** 黄杨钿甜佩戴价值230万元的GRAFF耳环引发对其父亲作为前公务员的财富来源质疑
*   **动画风格与目的（若聊天记录提及）：** 新闻调查式，清晰呈现事实同时带有悬念感，采用现代简约的信息图表式展示，突出核心争议点

## 2. 画布尺寸与输出格式
*   **画布尺寸：** 1920×1080像素 (宽高比 16:9，高清格式)
*   **最终输出格式：** 动画视频

## 3. 主要组成部分

### 3.1 人物元素列表
*   **人物名称/代号：** 黄杨钿甜
    *   **描述：** 事件的核心人物，童星，因在成人礼佩戴高价耳环引发对其家庭背景和财富来源的广泛争议
    *   **资产路径：** ../data/assets/characters/yhdt.jpg
    *   **宽高比：** 0.75 (3:4比例)

*   **人物名称/代号：** 黄杨钿甜父亲
    *   **描述：** 黄杨钿甜的父亲，作为前公务员身份的视觉呈现，用于与高价饰品形成对比
    *   **资产路径：** ../data/assets/characters/yhdt_f.jpg
    *   **宽高比：** 0.75 (3:4比例)
    
### 3.2 道具元素列表
*   **道具名称/代号：** 黄杨钿甜佩戴的GRAFF耳环
    *   **描述：** 黄杨钿甜成人礼上佩戴的格拉夫（GRAFF）祖母绿钻石耳环，公价传闻约230万元，是引发整个事件的导火索
    *   **资产路径：** ../data/assets/props/yhdt_earring.jpg
    *   **宽高比：** 1.00 (1:1比例)

### 3.3 背景设计
*   **背景类型：** 纯色
*   **颜色/资产路径：** 白色 (#FFFFFF)
*   **动画效果（若有）：** 无动画效果，保持静态纯白背景

### 3.4 文字关键词元素
*   **文字内容：** "引爆点"
    *   **出现时间范围：** 0s - 12s
    *   **位置（锚点或左上角坐标）：** (-6, 3.5) 左上角
    *   **字体：** [聊天记录未提及，建议使用黑体]
    *   **字号：** [聊天记录未提及，建议使用48px]
    *   **颜色：** 黑色

*   **文字内容：** "GRAFF祖母绿钻石耳环"
    *   **出现时间范围：** 2s - 4s
    *   **位置（锚点或左上角坐标）：** (5, -3) 右下角
    *   **字体：** [聊天记录未提及，建议使用无衬线体如Arial/Helvetica]
    *   **字号：** [聊天记录未提及，建议使用32px]
    *   **颜色：** 黑色

*   **文字内容：** "¥230万"
    *   **出现时间范围：** 4s - 8s
    *   **位置（锚点或左上角坐标）：** (0, 1) 画面中央偏上
    *   **字体：** [聊天记录未提及，建议使用加粗数字字体]
    *   **字号：** [聊天记录未提及，建议使用64px]
    *   **颜色：** 红色

*   **文字内容：** ""耳环是找妈妈的""
    *   **出现时间范围：** 6s - 8s
    *   **位置（锚点或左上角坐标）：** (0, -2) 画面底部中央
    *   **字体：** [聊天记录未提及，建议使用引用式字体如楷体]
    *   **字号：** [聊天记录未提及，建议使用36px]
    *   **颜色：** 淡蓝色

*   **文字内容：** "前公务员身份"
    *   **出现时间范围：** 8s - 12s
    *   **位置（锚点或左上角坐标）：** (-4, -2) 左侧父亲图片下方
    *   **字体：** [聊天记录未提及，建议使用黑体]
    *   **字号：** [聊天记录未提及，建议使用32px]
    *   **颜色：** 棕色

*   **文字内容：** "VS"
    *   **出现时间范围：** 8s - 12s
    *   **位置（锚点或左上角坐标）：** (0, 0) 画面中央
    *   **字体：** [聊天记录未提及，建议使用黑体加粗]
    *   **字号：** [聊天记录未提及，建议使用56px]
    *   **颜色：** 黑色

*   **文字内容：** "财富来源?"
    *   **出现时间范围：** 10s - 12s
    *   **位置（锚点或左上角坐标）：** (0, -3) 画面中央偏下
    *   **字体：** [聊天记录未提及，建议使用黑体加粗]
    *   **字号：** [聊天记录未提及，建议使用48px，问号放大到72px]
    *   **颜色：** 黑色

## 4. 分镜/场景与音频同步列表

*   **时间戳：** 0s ~ 2s
    *   **音频/独白内容：** "黄杨钿甜在其成人礼上佩戴的"
    *   **涉及元素 (按出场顺序列出)：** 黄杨钿甜, 引爆点
    *   **视觉构成与布局 (基于14x8单位网格，中心点(0,0))：**
        1.  **主要人物/道具元素：**
            *   **元素名称：** 黄杨钿甜
            *   **视觉参考：** 黄杨钿甜的清晰肖像照
            *   **尺寸 (宽x高)：** 6x8 单位
            *   **位置 (X,Y 相对于视频中心点(0,0))：** -2, 0 单位
        2.  **文字关键词元素：**
            *   **元素名称：** 引爆点
            *   **显示文本：** "引爆点"
            *   **位置：** (-6, 3.5) 左上角
            *   **字体/大小/颜色：** 黑体，大，黑色
    *   **动画与状态描述：** 白色背景上，黄杨钿甜照片从画面中心轻微放大进场，最终定位在画面左侧2/3区域。同时，标题"引爆点"从顶部滑入并固定在左上角位置。

*   **时间戳：** 2s ~ 4s
    *   **音频/独白内容：** "格拉夫祖母绿钻石耳环，"
    *   **涉及元素 (按出场顺序列出)：** 黄杨钿甜, 黄杨钿甜佩戴的GRAFF耳环, GRAFF祖母绿钻石耳环
    *   **视觉构成与布局 (基于14x8单位网格，中心点(0,0))：**
        1.  **主要人物/道具元素：**
            *   **元素名称：** 黄杨钿甜
            *   **视觉参考：** 黄杨钿甜的清晰肖像照
            *   **尺寸 (宽x高)：** 3x4 单位
            *   **位置 (X,Y 相对于视频中心点(0,0))：** -4, 0 单位
        2.  **次要人物/道具元素：**
            *   **元素名称：** 黄杨钿甜佩戴的GRAFF耳环
            *   **视觉参考：** 耳环清晰图片
            *   **尺寸 (宽x高)：** 4x4 单位
            *   **位置 (X,Y)：** 相对于视频中心点(0,0)：3, 0 单位
        3.  **文字关键词元素：**
            *   **元素名称：** GRAFF祖母绿钻石耳环
            *   **显示文本：** "GRAFF祖母绿钻石耳环"
            *   **位置：** 位于(5, -3)，耳环图片的右下方
            *   **字体/大小/颜色：** 无衬线体，中等，黑色
    *   **动画与状态描述：** 黄杨钿甜照片缩小至画面左侧1/3位置，耳环图片从小到大缓慢放大并旋转进入画面右侧，伴随着闪光音效。耳环进入画面时，产品名称文字"GRAFF祖母绿钻石耳环"同时出现在右下角。

*   **时间戳：** 4s ~ 6s
    *   **音频/独白内容：** "市场估价约二百三十万元，"
    *   **涉及元素 (按出场顺序列出)：** 黄杨钿甜, 黄杨钿甜佩戴的GRAFF耳环, ¥230万
    *   **视觉构成与布局 (基于14x8单位网格，中心点(0,0))：**
        1.  **主要人物/道具元素：**
            *   **元素名称：** 黄杨钿甜
            *   **视觉参考：** 黄杨钿甜的缩小肖像照
            *   **尺寸 (宽x高)：** 2x2.7 单位
            *   **位置 (X,Y 相对于视频中心点(0,0))：** -6, 2 单位
        2.  **次要人物/道具元素：**
            *   **元素名称：** 黄杨钿甜佩戴的GRAFF耳环
            *   **视觉参考：** 耳环清晰图片
            *   **尺寸 (宽x高)：** 2x2 单位
            *   **位置 (X,Y)：** 相对于视频中心点(0,0)：6, 2 单位
        3.  **文字关键词元素：**
            *   **元素名称：** ¥230万
            *   **显示文本：** "¥230万"
            *   **位置：** 位于(0, 1)，画面中央偏上
            *   **字体/大小/颜色：** 加粗数字字体，大，红色
    *   **动画与状态描述：** 人物照片和耳环图片同时缩小并移至画面左右边缘位置，为中央价格文字腾出完全独立的空间。价格"¥230万"以跳动计数效果在画面中央显示，从¥0快速跳到¥230万，伴随计数音效。数字大小随计数增加而放大，最终以醒目的红色加粗字体占据中央位置。

*   **时间戳：** 6s ~ 8s
    *   **音频/独白内容：** "她曾在社交平台表示"耳环是找妈妈的"。"
    *   **涉及元素 (按出场顺序列出)：** 黄杨钿甜, 黄杨钿甜佩戴的GRAFF耳环, ¥230万, "耳环是找妈妈的"
    *   **视觉构成与布局 (基于14x8单位网格，中心点(0,0))：**
        1.  **主要人物/道具元素：**
            *   **元素名称：** 黄杨钿甜
            *   **视觉参考：** 黄杨钿甜的缩小肖像照
            *   **尺寸 (宽x高)：** 2x2.7 单位
            *   **位置 (X,Y 相对于视频中心点(0,0))：** -6, 0 单位
        2.  **次要人物/道具元素：**
            *   **元素名称：** 黄杨钿甜佩戴的GRAFF耳环
            *   **视觉参考：** 耳环清晰图片
            *   **尺寸 (宽x高)：** 2x2 单位
            *   **位置 (X,Y)：** 相对于视频中心点(0,0)：6, 0 单位
        3.  **文字关键词元素：**
            *   **元素名称：** ¥230万
            *   **显示文本：** "¥230万"
            *   **位置：** 位于(0, 2)，画面上方中央
            *   **字体/大小/颜色：** 加粗数字字体，稍小，红色
            *   **元素名称：** "耳环是找妈妈的"
            *   **显示文本：** ""耳环是找妈妈的""
            *   **位置：** 位于(0, -2)，画面下方中央
            *   **字体/大小/颜色：** 引用式字体，中等，淡蓝色
    *   **动画与状态描述：** 黄杨钿甜照片和耳环图片保持在画面左右两侧。价格文字缓慢上移并减小体积，为引用文字留出空间。引用文字""耳环是找妈妈的""以打字机效果从左到右依次显示在画面底部中央位置，伴随轻微的打字效果音，使用引用式字体和淡蓝色，与上方价格文字保持明显间距。

*   **时间戳：** 8s ~ 10s
    *   **音频/独白内容：** "这一高价饰品与其父亲作为前公务员的身份"
    *   **涉及元素 (按出场顺序列出)：** 黄杨钿甜父亲, 前公务员身份, 黄杨钿甜佩戴的GRAFF耳环, VS
    *   **视觉构成与布局 (基于14x8单位网格，中心点(0,0))：**
        1.  **主要人物/道具元素：**
            *   **元素名称：** 黄杨钿甜父亲
            *   **视觉参考：** 父亲肖像照
            *   **尺寸 (宽x高)：** 4x5.3 单位
            *   **位置 (X,Y 相对于视频中心点(0,0))：** -4, 0 单位
            *   **元素名称：** 黄杨钿甜佩戴的GRAFF耳环
            *   **视觉参考：** 耳环清晰图片
            *   **尺寸 (宽x高)：** 4x4 单位
            *   **位置 (X,Y 相对于视频中心点(0,0))：** 4, 0 单位
        2.  **文字关键词元素：**
            *   **元素名称：** 前公务员身份
            *   **显示文本：** "前公务员身份"
            *   **位置：** 位于(-4, -2)，父亲图片下方
            *   **字体/大小/颜色：** 黑体，中等，棕色
            *   **元素名称：** VS
            *   **显示文本：** "VS"
            *   **位置：** 位于(0, 0)，画面中央
            *   **字体/大小/颜色：** 黑体加粗，大，黑色
    *   **动画与状态描述：** 黄杨钿甜照片淡出，父亲照片从左侧滑入画面，定位在左侧，下方同时显示"前公务员身份"文字标签；右侧显示耳环图片。画面中央出现醒目的"VS"对比符号，连接左右两个元素。当这些元素在画面中相遇时，伴随"不和谐"的短促音效，并产生轻微震动效果，强调对比的戏剧性。

*   **时间戳：** 10s ~ 12s
    *   **音频/独白内容：** "形成强烈反差，引发公众对家庭财富来源的质疑。"
    *   **涉及元素 (按出场顺序列出)：** 黄杨钿甜父亲, 黄杨钿甜佩戴的GRAFF耳环, VS, 前公务员身份, 财富来源?
    *   **视觉构成与布局 (基于14x8单位网格，中心点(0,0))：**
        1.  **主要人物/道具元素：**
            *   **元素名称：** 黄杨钿甜父亲
            *   **视觉参考：** 父亲肖像照，缩小版本
            *   **尺寸 (宽x高)：** 3x4 单位
            *   **位置 (X,Y 相对于视频中心点(0,0))：** -4, 1 单位
            *   **元素名称：** 黄杨钿甜佩戴的GRAFF耳环
            *   **视觉参考：** 耳环清晰图片，缩小版本
            *   **尺寸 (宽x高)：** 3x3 单位
            *   **位置 (X,Y 相对于视频中心点(0,0))：** 4, 1 单位
        2.  **文字关键词元素：**
            *   **元素名称：** VS
            *   **显示文本：** "VS"
            *   **位置：** 位于(0, 1)，画面中央
            *   **字体/大小/颜色：** 黑体加粗，中等，黑色
            *   **元素名称：** 前公务员身份
            *   **显示文本：** "前公务员身份"
            *   **位置：** 位于(-4, -0.5)，父亲图片下方
            *   **字体/大小/颜色：** 黑体，小，棕色
            *   **元素名称：** 财富来源?
            *   **显示文本：** "财富来源?"
            *   **位置：** 位于(0, -3)，画面底部中央
            *   **字体/大小/颜色：** 黑体加粗，大，黑色，问号特别放大
    *   **动画与状态描述：** 父亲照片与耳环图片保持在画面两侧，但体积缩小并向上移动，VS符号也相应缩小并上移，为底部质疑文字腾出空间。"财富来源?"文字从下方弹出，问号符号特别放大。在"质疑"一词音频出现时（约11秒处），问号有额外的脉冲放大效果，同时配合悬疑音效，形成视听结合的高潮点。问号持续轻微跳动直至视频结束。

## 5. 重要说明与约束
*   **素材引用：** 所有图片素材严格使用 `3.1` 和 `3.2` 中提取的 `资产路径`。
*   **坐标系：** 
    * 画布尺寸：14个宽度单位 × 8个高度单位
    * 坐标系原点：画布中心点(0,0)
    * X轴范围：-7到+7（左负右正）
    * Y轴范围：-4到+4（下负上正）
    * 所有元素位置必须基于此中心坐标系进行定位
*   **尺寸与比例：** 所有的图片素材只给出了宽高比，具体尺寸需要根据实际画布大小和布局计算出高度，宽度自适应。
*   **元素不重叠：** 除非设计意图或聊天记录明确指示，否则应避免动画元素在视觉上不期望地重叠。
    **重要：** Y轴坐标系中，正值表示元素中心点在画布中心点 上方，负值表示在 下方。AI在转换为CSS transform 时需注意此对应关系 (例如，设计文档Y=1，则CSS translateY 的计算结果应使元素上移)。
        """,
        enable_thinking=True,
        thinking_budget_tokens=2000
    )
    print(f"响应: {response}")

if __name__ == "__main__":
    # 运行异步主函数
    asyncio.run(main())