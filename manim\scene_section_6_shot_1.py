from manim import *
import os
from layout_manager import LayoutManager

'''
========== 动画素材清单 ==========

## 图片素材：
- 耳环图标: 耳环图标.png - 代表"天价耳环"这一事件核心
- 放大镜图标: 放大镜图标.png - 象征深入审视和调查
- 文件图标: 文件图标.png - 代表官方调查
- 官方印章图标: 官方印章图标.png - 象征官方确认的印章或公章
- 问号图标: 问号图标.png - 代表未解答的问题
- 金钱图标: 金钱图标.png - 代表财富或金钱
- 互联网图标: 互联网图标.png - 代表互联网或网络
- 群众图标: 群众图标.png - 代表民众或公众力量
- 警示图标: 警示图标.png - 用于强调违规行为

## 文字关键词素材：
- "要点回顾" - 0s-3s - 黑体32pt黑色
- "黄杨钿甜'天价耳环'事件" - 0s-3s - 黑体26pt黑色
- "超越明星炫富" - 3s-8s - 黑体24pt深红色
- "多维审视" - 3s-8s - 黑体24pt深蓝色
- "从个人行为到社会议题的转变" - 3s-8s - 黑体20pt黑色
- "官方确认" - 8s-14s - 黑体26pt深蓝色
- "违规经商 隐瞒超生" - 8s-14s - 黑体24pt深红色
- "未解问题" - 14s-20s - 黑体26pt深红色
- "家族巨额财富来源?" - 14s-20s - 黑体24pt金黄色
- "耳环真伪?" - 14s-20s - 黑体24pt金黄色
- "社会意义" - 20s-25s - 黑体26pt深绿色
- "权力与资本关系" - 20s-25s - 黑体24pt深蓝色
- "公众参与监督" - 20s-25s - 黑体24pt深绿色
- "互联网时代的公众参与" - 25s-30s - 黑体28pt深绿色
- "民间监督力量的崛起" - 25s-30s - 黑体22pt黑色

## 动画总时长：30秒
=====================================
'''

class YellowYangDiantianEarringEvent(Scene):
    def construct(self):
        # 设置背景色
        self.camera.background_color = "#FFFFFF"
        
        # 检查图片资源是否存在
        image_paths = {
            "earring": "耳环图标.png",
            "magnifier": "放大镜图标.png",
            "document": "文件图标.png",
            "official_seal": "官方印章图标.png",
            "question_mark": "问号图标.png",
            "money": "金钱图标.png",
            "internet": "互联网图标.png",
            "crowd": "群众图标.png",
            "warning": "警示图标.png"
        }
        
        for name, path in image_paths.items():
            if not os.path.exists(path):
                print(f"警告: 图片文件 {path} 不存在!")
        
        # 第一阶段 (0s-3s): 开场介绍
        # 创建标题文字
        title = Text("要点回顾", font="SimHei", font_size=32, color=BLACK)
        title.move_to([0, 3, 0])
        title.set_opacity(0)
        
        subtitle = Text("黄杨钿甜'天价耳环'事件", font="SimHei", font_size=26, color=BLACK)
        subtitle.move_to([0, 2, 0])
        subtitle.set_opacity(0)
        
        # 打字机效果显示标题
        for i in range(len(title.text)):
            self.play(
                title[i].animate.set_opacity(1),
                run_time=0.1
            )
        
        # 副标题滑入
        self.play(
            subtitle.animate.set_opacity(1),
            run_time=0.5
        )
        
        # 等待时间以匹配总时长
        self.wait(0.5)
        
        # 第二阶段 (3s-8s): 事件性质转变
        # 创建图标和文字
        if os.path.exists(image_paths["earring"]):
            earring_icon = ImageMobject(image_paths["earring"])
            earring_icon.height = 3
            earring_icon.width = 3  # 宽高比1:1
            earring_icon.move_to([-4, 1, 0])
            earring_icon.set_opacity(0)
        else:
            # 如果图片不存在，创建一个占位符
            earring_icon = Circle(radius=1.5, color=GREY)
            earring_icon.move_to([-4, 1, 0])
            earring_icon.set_opacity(0)
        
        if os.path.exists(image_paths["magnifier"]):
            magnifier_icon = ImageMobject(image_paths["magnifier"])
            magnifier_icon.height = 3
            magnifier_icon.width = 3  # 宽高比1:1
            magnifier_icon.move_to([4, 1, 0])
            magnifier_icon.set_opacity(0)
        else:
            magnifier_icon = Circle(radius=1.5, color=GREY)
            magnifier_icon.move_to([4, 1, 0])
            magnifier_icon.set_opacity(0)
        
        text_celebrity = Text("超越明星炫富", font="SimHei", font_size=24, color="#922B21")
        text_celebrity.move_to([-3.5, 0, 0])
        text_celebrity.set_opacity(0)
        
        text_multi_dimension = Text("多维审视", font="SimHei", font_size=24, color="#1A5276")
        text_multi_dimension.move_to([3.5, 0, 0])
        text_multi_dimension.set_opacity(0)
        
        text_transformation = Text("从个人行为到社会议题的转变", font="SimHei", font_size=20, color=BLACK)
        text_transformation.move_to([0, -2, 0])
        text_transformation.set_opacity(0)
        
        # 清除第一阶段内容
        self.play(
            FadeOut(title),
            FadeOut(subtitle),
            run_time=0.5
        )
        
        # 图标滑入
        self.play(
            earring_icon.animate.set_opacity(1),
            magnifier_icon.animate.set_opacity(1),
            run_time=1
        )
        
        # 文字淡入并放大强调
        self.play(
            text_celebrity.animate.set_opacity(1).scale(1.1),
            run_time=0.5
        )
        self.play(
            text_celebrity.animate.scale(1/1.1),
            run_time=0.3
        )
        
        self.play(
            text_multi_dimension.animate.set_opacity(1).scale(1.1),
            run_time=0.5
        )
        self.play(
            text_multi_dimension.animate.scale(1/1.1),
            run_time=0.3
        )
        
        # 底部文字平滑浮现
        self.play(
            text_transformation.animate.set_opacity(1),
            run_time=0.7
        )
        
        # 等待时间以匹配总时长
        self.wait(1.2)
        
        # 第三阶段 (8s-14s): 官方确认
        # 创建图标和文字
        if os.path.exists(image_paths["official_seal"]):
            seal_icon = ImageMobject(image_paths["official_seal"])
            seal_icon.height = 2.5
            seal_icon.width = 2.5  # 宽高比1:1
            seal_icon.move_to([-2, 1, 0])
            seal_icon.set_opacity(0)
        else:
            seal_icon = Circle(radius=1.25, color=GREY)
            seal_icon.move_to([-2, 1, 0])
            seal_icon.set_opacity(0)
        
        if os.path.exists(image_paths["document"]):
            document_icon = ImageMobject(image_paths["document"])
            document_icon.height = 2.5
            document_icon.width = 2.5  # 宽高比1:1
            document_icon.move_to([2, 1, 0])
            document_icon.set_opacity(0)
        else:
            document_icon = Rectangle(height=2.5, width=2, color=GREY)
            document_icon.move_to([2, 1, 0])
            document_icon.set_opacity(0)
        
        if os.path.exists(image_paths["warning"]):
            warning_icon = ImageMobject(image_paths["warning"])
            warning_icon.height = 2
            warning_icon.width = 2  # 宽高比1:1
            warning_icon.move_to([0, -1, 0])
            warning_icon.set_opacity(0)
        else:
            warning_icon = Triangle(color=RED)
            warning_icon.height = 2
            warning_icon.move_to([0, -1, 0])
            warning_icon.set_opacity(0)
        
        text_official = Text("官方确认", font="SimHei", font_size=26, color="#1A5276")
        text_official.move_to([0, 3, 0])
        text_official.set_opacity(0)
        
        text_violation = Text("违规经商 隐瞒超生", font="SimHei", font_size=24, color="#922B21")
        text_violation.move_to([0, 0, 0])
        text_violation.set_opacity(0)
        
        # 清除第二阶段内容
        self.play(
            *[FadeOut(mob) for mob in [earring_icon, magnifier_icon, text_celebrity, 
                                      text_multi_dimension, text_transformation]],
            run_time=0.5
        )
        
        # 新内容从右侧滑入
        self.play(
            seal_icon.animate.set_opacity(1),
            document_icon.animate.set_opacity(1),
            run_time=1
        )
        
        # 标题文字淡入
        self.play(
            text_official.animate.set_opacity(1),
            run_time=0.5
        )
        
        # 警示图标出现
        self.play(
            warning_icon.animate.set_opacity(1),
            run_time=0.5
        )
        
        # 违规文字出现，带光晕效果
        self.play(
            text_violation.animate.set_opacity(1),
            run_time=0.5
        )
        
        # 光晕效果
        glow = text_violation.copy().set_color(RED).set_opacity(0.3)
        self.play(
            glow.animate.scale(1.2).set_opacity(0),
            run_time=1
        )
        
        # 等待时间以匹配总时长
        self.wait(2)
        
        # 第四阶段 (14s-20s): 未解问题
        # 创建图标和文字
        if os.path.exists(image_paths["question_mark"]):
            question_icon = ImageMobject(image_paths["question_mark"])
            question_icon.height = 3
            question_icon.width = 3  # 宽高比1:1
            question_icon.move_to([0, 2, 0])
            question_icon.set_opacity(0)
        else:
            question_icon = Text("?", font_size=72, color=GOLD)
            question_icon.move_to([0, 2, 0])
            question_icon.set_opacity(0)
        
        if os.path.exists(image_paths["money"]):
            money_icon = ImageMobject(image_paths["money"])
            money_icon.height = 2.5
            money_icon.width = 2.5  # 宽高比1:1
            money_icon.move_to([-3.5, 1, 0])
            money_icon.set_opacity(0)
        else:
            money_icon = Circle(radius=1.25, color=GOLD)
            money_icon.move_to([-3.5, 1, 0])
            money_icon.set_opacity(0)
        
        text_unsolved = Text("未解问题", font="SimHei", font_size=26, color="#922B21")
        text_unsolved.move_to([0, 3, 0])
        text_unsolved.set_opacity(0)
        
        text_wealth = Text("家族巨额财富来源?", font="SimHei", font_size=24, color="#B7950B")
        text_wealth.move_to([-3.5, 0, 0])
        text_wealth.set_opacity(0)
        
        text_earring = Text("耳环真伪?", font="SimHei", font_size=24, color="#B7950B")
        text_earring.move_to([3.5, 0, 0])
        text_earring.set_opacity(0)
        
        # 清除第三阶段内容
        self.play(
            *[FadeOut(mob) for mob in [seal_icon, document_icon, warning_icon, 
                                      text_official, text_violation]],
            run_time=0.5
        )
        
        # 问号图标从中心放大出现
        self.play(
            question_icon.animate.set_opacity(1).scale(0.1).scale(10),
            run_time=1
        )
        
        # 标题文字淡入
        self.play(
            text_unsolved.animate.set_opacity(1),
            run_time=0.5
        )
        
        # 金钱图标淡入
        self.play(
            money_icon.animate.set_opacity(1),
            run_time=0.5
        )
        
        # 财富来源问题文字出现
        self.play(
            text_wealth.animate.set_opacity(1),
            run_time=0.5
        )
        
        # 耳环真伪问题文字出现，从中心向两侧扩散效果
        self.play(
            text_earring.animate.set_opacity(1).scale(0.1).scale(10),
            run_time=0.8
        )
        
        # 等待时间以匹配总时长
        self.wait(2.2)
        
        # 第五阶段 (20s-25s): 社会意义
        # 创建图标和文字
        if os.path.exists(image_paths["internet"]):
            internet_icon = ImageMobject(image_paths["internet"])
            internet_icon.height = 3
            internet_icon.width = 3  # 宽高比1:1
            internet_icon.move_to([-3, 1, 0])
            internet_icon.set_opacity(0)
        else:
            internet_icon = Circle(radius=1.5, color=BLUE)
            internet_icon.move_to([-3, 1, 0])
            internet_icon.set_opacity(0)
        
        if os.path.exists(image_paths["crowd"]):
            crowd_icon = ImageMobject(image_paths["crowd"])
            crowd_icon.height = 3
            crowd_icon.width = 3  # 宽高比1:1
            crowd_icon.move_to([3, 1, 0])
            crowd_icon.set_opacity(0)
        else:
            crowd_icon = VGroup(*[Dot(radius=0.2) for _ in range(5)])
            crowd_icon.arrange_in_grid(rows=2, buff=0.4)
            crowd_icon.move_to([3, 1, 0])
            crowd_icon.set_opacity(0)
        
        text_social = Text("社会意义", font="SimHei", font_size=26, color="#117A65")
        text_social.move_to([0, 3, 0])
        text_social.set_opacity(0)
        
        text_power = Text("权力与资本关系", font="SimHei", font_size=24, color="#1A5276")
        text_power.move_to([-3.5, -2, 0])
        text_power.set_opacity(0)
        
        text_public = Text("公众参与监督", font="SimHei", font_size=24, color="#117A65")
        text_public.move_to([3.5, -2, 0])
        text_public.set_opacity(0)
        
        # 清除第四阶段内容
        self.play(
            *[FadeOut(mob) for mob in [question_icon, money_icon, text_unsolved, 
                                      text_wealth, text_earring]],
            run_time=0.5
        )
        
        # 交叉消隐转场
        self.play(
            FadeIn(internet_icon),
            FadeIn(crowd_icon),
            run_time=1
        )
        
        # 标题文字从上方滑入
        self.play(
            text_social.animate.set_opacity(1).shift(DOWN*0.5).shift(UP*0.5),
            run_time=0.5
        )
        
        # 权力与资本关系文字通过缩放效果出现
        self.play(
            text_power.animate.set_opacity(1).scale(1.2).scale(1/1.2),
            run_time=0.8
        )
        
        # 公众参与监督文字出现
        self.play(
            text_public.animate.set_opacity(1).scale(1.2).scale(1/1.2),
            run_time=0.8
        )
        
        # 等待时间以匹配总时长
        self.wait(1.4)
        
        # 第六阶段 (25s-30s): 结论
        # 创建图标和文字
        if os.path.exists(image_paths["internet"]):
            internet_icon_final = ImageMobject(image_paths["internet"])
            internet_icon_final.height = 4
            internet_icon_final.width = 4  # 宽高比1:1
            internet_icon_final.move_to([-2, 0.5, 0])
            internet_icon_final.set_opacity(0)
        else:
            internet_icon_final = Circle(radius=2, color=BLUE)
            internet_icon_final.move_to([-2, 0.5, 0])
            internet_icon_final.set_opacity(0)
        
        if os.path.exists(image_paths["crowd"]):
            crowd_icon_final = ImageMobject(image_paths["crowd"])
            crowd_icon_final.height = 4
            crowd_icon_final.width = 4  # 宽高比1:1
            crowd_icon_final.move_to([2, 0.5, 0])
            crowd_icon_final.set_opacity(0)
        else:
            crowd_icon_final = VGroup(*[Dot(radius=0.3) for _ in range(5)])
            crowd_icon_final.arrange_in_grid(rows=2, buff=0.5)
            crowd_icon_final.move_to([2, 0.5, 0])
            crowd_icon_final.set_opacity(0)
        
        text_internet_era = Text("互联网时代的公众参与", font="SimHei", font_size=28, color="#117A65")
        text_internet_era.move_to([0, 0, 0])
        text_internet_era.set_opacity(0)
        
        text_rise = Text("民间监督力量的崛起", font="SimHei", font_size=22, color=BLACK)
        text_rise.move_to([0, -1, 0])
        text_rise.set_opacity(0)
        
        # 清除第五阶段内容
        self.play(
            *[FadeOut(mob) for mob in [internet_icon, crowd_icon, text_social, 
                                      text_power, text_public]],
            run_time=0.5
        )
        
        # 图标从两侧向中心汇聚
        self.play(
            internet_icon_final.animate.set_opacity(1).scale(0.5).scale(2),
            crowd_icon_final.animate.set_opacity(1).scale(0.5).scale(2),
            run_time=1
        )
        
        # 主题句从小到大居中出现
        self.play(
            text_internet_era.animate.set_opacity(1).scale(0.5).scale(2),
            run_time=1
        )
        
        # 结语文字缓慢淡入
        self.play(
            text_rise.animate.set_opacity(1),
            run_time=1
        )
        
        # 所有元素汇聚形成统一视觉焦点，然后缩小聚焦
        self.play(
            internet_icon_final.animate.scale(0.8).shift(UP*0.2),
            crowd_icon_final.animate.scale(0.8).shift(UP*0.2),
            text_internet_era.animate.shift(UP*0.2),
            text_rise.animate.shift(UP*0.2),
            run_time=1
        )
        
        # 最后淡出
        self.play(
            *[FadeOut(mob) for mob in [internet_icon_final, crowd_icon_final, 
                                      text_internet_era, text_rise]],
            run_time=0.5
        )