from manim import *
import os
from layout_manager import LayoutManager

'''
========== 动画素材清单 ==========

## 图片素材：
- 黄杨钿甜的父亲: image.character - 黄杨钿甜父亲的正面照片
- 深圳别墅: image.prop - 豪华深圳别墅完整图片

## 文字关键词素材：
- "背景挖掘" - 0s-16s - 黑色微粗Sans-serif字体
- "前雅安市公务员 杨伟(杨某)" - 0s-3s - 黑色Sans-serif字体
- "公职生涯(2010-2017)" - 4s-7s - 红色加粗Sans-serif字体
- "2010-2015: 城管局" - 4s-7s - 黑色Sans-serif字体
- "2015-2017: 投资促进局" - 4s-7s - 黑色Sans-serif字体
- "上亿豪宅" - 8s-12s - 红色加粗Sans-serif字体
- "位置：深圳" - 8s-12s - 黑色Sans-serif字体
- "及多件奢侈品" - 8s-12s - 黑色Sans-serif字体
- "财富与收入对比" - 13s-16s - 黑色Sans-serif字体
- "公务员收入水平" - 13s-16s - 黑色Sans-serif字体
- "家庭实际财富" - 13s-16s - 黑色Sans-serif字体
- "收入严重不符" - 13s-16s - 红色加粗Sans-serif字体
- "财富来源何处？" - 14s-16s - 黑色微粗斜体字

## 动画总时长：16秒
=====================================
'''

class BackgroundInvestigation(Scene):
    def construct(self):
        # 设置背景色
        self.camera.background_color = "#FFFFFF"
        import os
        script_dir = os.path.dirname(os.path.abspath(__file__))
        character_path = os.path.join(script_dir, "杨伟肖像.png")
        prop_path = os.path.join(script_dir, "bieshu.jpg")

        if not os.path.exists(character_path):
            print(f"警告: 图片文件不存在 - {character_path}")
        else:
            print(f"成功找到图片文件: {character_path}")

        if not os.path.exists(prop_path):
            print(f"警告: 图片文件不存在 - {prop_path}")
        else:
            print(f"成功找到图片文件: {prop_path}")

        # 创建图片元素
        try:
            character = ImageMobject(character_path)
            # 设置宽高比 3:4
            character.height = 4
            character.width = 3

            villa = ImageMobject(prop_path)
            # 设置宽高比 16:9
            villa.height = 4.5
            villa.width = 8
        except:
            # 如果图片加载失败，创建占位符
            character = Rectangle(height=4, width=3, color=GRAY)

            villa = Rectangle(height=4.5, width=8, color=GRAY)

        # 创建文字元素
        title = Text("背景挖掘", font="SimHei", font_size=40, color=BLACK, weight=BOLD)
        title.to_edge(UP, buff=0.5)

        # 第一阶段文字
        identity = Text("前雅安市公务员 杨伟(杨某)", font="SimHei", font_size=28, color=BLACK)

        # 第二阶段文字
        career_title = Text("公职生涯(2010-2017)", font="SimHei", font_size=32, color=RED, weight=BOLD)

        job1 = Text("2010-2015: 城管局", font="SimHei", font_size=24, color=BLACK)

        job2 = Text("2015-2017: 投资促进局", font="SimHei", font_size=24, color=BLACK)

        # 第三阶段文字
        villa_title = Text("上亿豪宅", font="SimHei", font_size=32, color=RED, weight=BOLD)

        location = Text("位置：深圳", font="SimHei", font_size=24, color=BLACK)

        luxury = Text("及多件奢侈品", font="SimHei", font_size=24, color=BLACK)

        # 第四阶段文字
        comparison = Text("财富与收入对比", font="SimHei", font_size=28, color=BLACK)

        income = Text("公务员收入水平", font="SimHei", font_size=24, color=BLACK)

        wealth = Text("家庭实际财富", font="SimHei", font_size=24, color=BLACK)

        mismatch = Text("收入严重不符", font="SimHei", font_size=28, color=RED, weight=BOLD)

        question = Text("财富来源何处？", font="SimHei", font_size=26, color=BLACK, slant=ITALIC, weight=BOLD)

        # 第一阶段 (0s-3s): 介绍人物
        # 设置初始位置
        character.move_to([0, 0, 0])
        character.scale(1.05)  # 初始放大5%

        # 标题位置
        title.move_to([0, 3.5, 0])

        # 身份标签位置
        identity.move_to([2, 0, 0])

        # 动画：人物图片缩小至正常大小，标题从上方滑入，身份标签渐显
        self.play(
            FadeIn(character, scale=1),
            character.animate.scale(1/1.05),
            run_time=1
        )

        self.play(
            FadeIn(title),
            run_time=0.5
        )

        self.play(
            FadeIn(identity),
            run_time=0.5
        )

        # 等待，确保总时长为3秒
        self.wait(1)

        # 第二阶段 (4s-7s): 公职生涯
        # 设置新位置
        character_new_pos = [-4.5, 0, 0]
        character_new_size = 2.7  # 新高度

        # 职业时间轴位置
        career_title.move_to([3, 2, 0])
        job1.move_to([3, 0, 0])
        job2.move_to([3, -1, 0])

        # 动画：人物图片缩小并移至左侧，显示职业时间轴
        self.play(
            character.animate.move_to(character_new_pos).set_height(character_new_size),
            FadeOut(identity),
            run_time=1
        )

        self.play(
            FadeIn(career_title),
            run_time=0.5
        )

        self.play(
            FadeIn(job1),
            run_time=0.75
        )

        self.play(
            FadeIn(job2),
            run_time=0.75
        )

        # 等待，确保总时长为3秒
        self.wait(1)

        # 第三阶段 (8s-12s): 豪宅展示
        # 设置豪宅位置
        villa.move_to([-1, 0, 0])

        # 豪宅相关文字位置
        villa_title.move_to([4, 2, 0])
        location.move_to([4, 0, 0])
        luxury.move_to([0, -3, 0])

        # 动画：淡出人物和职业信息，显示豪宅和相关信息
        self.play(
            FadeOut(character),
            FadeOut(career_title),
            FadeOut(job1),
            FadeOut(job2),
            FadeIn(villa),
            run_time=1
        )

        self.play(
            FadeIn(villa_title),
            run_time=0.5
        )

        self.play(
            FadeIn(location),
            run_time=0.5
        )

        self.play(
            FadeIn(luxury),
            run_time=0.5
        )

        # 等待，确保总时长为4秒
        self.wait(1.5)

        # 第四阶段 (13s-16s): 财富对比
        # 重新设置人物和豪宅位置及大小
        character.set_height(2.7)
        character.move_to([-4.5, 0, 0])

        villa.set_height(2.25)
        villa.set_width(4)
        villa.move_to([4.5, 0, 0])

        # 对比相关文字位置
        comparison.move_to([0, 3, 0])
        income.move_to([-3.5, 2, 0])
        wealth.move_to([3.5, 2, 0])
        mismatch.move_to([0, -2, 0])
        question.move_to([0, -3.5, 0])

        # VS符号
        vs = Text("VS", font_size=36, color=BLACK, weight=BOLD)
        vs.move_to([0, 0, 0])

        # 动画：显示对比布局
        self.play(
            FadeOut(villa_title),
            FadeOut(location),
            FadeOut(luxury),
            villa.animate.move_to([4.5, 0, 0]).set_height(2.25).set_width(4),
            FadeIn(character),
            FadeIn(comparison),
            run_time=1
        )

        self.play(
            FadeIn(income),
            FadeIn(wealth),
            FadeIn(vs),
            run_time=0.5
        )

        self.play(
            FadeIn(mismatch),
            run_time=0.5
        )

        # 最后显示问题
        self.play(
            FadeIn(question),
            run_time=0.5
        )

        # 等待，确保总时长为3秒
        self.wait(0.5)

        # 确保LayoutManager检查元素不重叠
        LayoutManager.print_layout_debug(title, "标题")
        LayoutManager.print_layout_debug(character, "人物图片")
        LayoutManager.print_layout_debug(villa, "豪宅图片")
        LayoutManager.ensure_screen_bounds(title)
        LayoutManager.ensure_screen_bounds(character)
        LayoutManager.ensure_screen_bounds(villa)