import asyncio
from typing import List, Dict, Any, <PERSON><PERSON>
from langchain_core.messages import BaseMessage, HumanMessage, SystemMessage, AIMessage
from llm.claude_local import ClaudeLocalProvider

class AIConversation:
    def __init__(self, 
                 ai1_name: str = "视频导演",
                 ai2_name: str = "剪辑师",
                 ai1_character: str = "是一个视频的导演",
                 ai2_character: str = "是一个视频的剪辑师",
                 script: Dict[str, Any] = None,
                 max_rounds: int = 5,
                 ai1_temperature: float = 1.0,
                 ai2_temperature: float = 1.0,
                 ai1_first: bool = False,
    ):
        """
        初始化AI对话系统
        
        Args:
            ai1_name: 第一个AI的名称
            ai2_name: 第二个AI的名称
            ai1_character: 第一个AI的角色描述
            ai2_character: 第二个AI的角色描述
            max_rounds: 最大对话轮数
            script: 脚本信息
            ai1_temperature: 第一个AI的温度参数，控制生成文本的随机性
            ai2_temperature: 第二个AI的温度参数，控制生成文本的随机性
            ai1_first: 是否让AI1先响应，默认False表示AI2先响应
        """
        self.ai1_name = ai1_name
        self.ai2_name = ai2_name
        self.ai1_character = ai1_character
        self.ai2_character = ai2_character
        self.max_rounds = max_rounds
        self.script = script
        self.ai1_temperature = ai1_temperature
        self.ai2_temperature = ai2_temperature
        self.ai1_first = ai1_first
        
        # 初始化两个AI的提供者
        self.ai1_provider = ClaudeLocalProvider()
        self.ai2_provider = ClaudeLocalProvider()
        
        # 设置系统提示词
        self.ai1_system_prompt = self.ai1_character
        self.ai2_system_prompt = self.ai2_character
        
        self.ai1_provider.set_system_prompt(self.ai1_system_prompt)
        self.ai2_provider.set_system_prompt(self.ai2_system_prompt)
        
        # 存储对话历史
        self.ai1_history: List[BaseMessage] = []
        self.ai2_history: List[BaseMessage] = []
        self.conversation_log: List[Dict[str, str]] = []
        
    async def start_conversation(self, initial_message: str = None, max_rounds: int = None, 
                              end_phrase: str = "对话结束") -> List[Dict[str, str]]:
        """
        开始AI之间的对话
        
        Args:
            initial_message: 初始消息
            max_rounds: 最大对话轮数，如果提供则覆盖初始化时设置的值
            end_phrase: 对话结束标志词，当AI1(导演)的消息中包含此词时提前结束对话
            
        Returns:
            List[Dict[str, str]]: 对话历史记录
        """
        # 如果提供了max_rounds参数，则覆盖初始化时设置的值
        current_round = 0
        actual_max_rounds = max_rounds if max_rounds is not None else self.max_rounds
        
        # 添加初始消息到对话历史记录
        if initial_message:
            self.ai1_history.append(HumanMessage(content=initial_message))
            self.ai2_history.append(HumanMessage(content=initial_message))
            self.conversation_log.append({
                "role": "user",
                "content": initial_message
            })
        
        while current_round < actual_max_rounds:
            if self.ai1_first:
                # AI1先响应的对话顺序
                # AI1的回合
                ai1_response = await self.ai1_provider.generate_response(
                    self.ai1_history,
                    temperature=self.ai1_temperature
                )
                # 更新AI1的历史记录
                self.ai1_history.append(AIMessage(content=ai1_response))
                # 更新AI2的历史记录
                self.ai2_history.append(HumanMessage(content=ai1_response))
                # 更新对话日志
                self.conversation_log.append({
                    "role": self.ai1_name,
                    "content": ai1_response
                })
                
                # 检查是否结束对话 - 在AI1的回答中检查end_phrase
                if end_phrase in ai1_response:
                    break
                
                # AI2的回合
                ai2_response = await self.ai2_provider.generate_response(
                    messages=self.ai2_history,
                    temperature=self.ai2_temperature
                )
                
                # 更新AI2的历史记录
                self.ai2_history.append(AIMessage(content=ai2_response))
                # 更新AI1的历史记录
                self.ai1_history.append(HumanMessage(content=ai2_response))
                # 更新对话日志
                self.conversation_log.append({
                    "role": self.ai2_name,
                    "content": ai2_response
                })
            else:
                # AI2先响应的对话顺序（原有逻辑）
                # AI2的回合
                ai2_response = await self.ai2_provider.generate_response(
                    messages=self.ai2_history,
                    temperature=self.ai2_temperature
                )
                
                # 更新AI2的历史记录
                self.ai2_history.append(AIMessage(content=ai2_response))
                # 更新AI1的历史记录
                self.ai1_history.append(HumanMessage(content=ai2_response))
                # 更新对话日志
                self.conversation_log.append({
                    "role": self.ai2_name,
                    "content": ai2_response
                })
                
                # AI1的回合
                ai1_response = await self.ai1_provider.generate_response(
                    self.ai1_history,
                    temperature=self.ai1_temperature
                )
                # 更新AI1的历史记录
                self.ai1_history.append(AIMessage(content=ai1_response))
                # 更新AI2的历史记录
                self.ai2_history.append(HumanMessage(content=ai1_response))
                # 更新对话日志
                self.conversation_log.append({
                    "role": self.ai1_name,
                    "content": ai1_response
                })
                
                # 检查是否结束对话 - 只在AI1(导演)的回答中检查end_phrase
                if end_phrase in ai1_response:
                    break
                
            current_round += 1
            
        return self.conversation_log

async def main():
    # 创建AI对话实例

    script = {
        "title": "引爆争议的奢侈品展示",
        "content": "事件起源于黄杨钿甜在2025年5月学校成人礼上佩戴的GRAFF祖母绿钻石耳环，被网友指出价值约230万元。当黄解释称珠宝是\"借用母亲的\"后，质疑不减反增，公众开始关注其家庭财富来源的合法性问题。",
        "key_points": [
        "黄杨钿甜在成人礼佩戴GRAFF祖母绿钻石耳环",
        "网友指出耳环价值约230万元",    
        "黄解释称珠宝是母亲的",
        "此回应引发更广泛质疑"
        ],
        "assets": {
        "characterAssets": [
            {
            "name": "黄杨钿甜",
            "description": "事件的核心人物，童星，因在成人礼佩戴高价耳环引发对其家庭背景和财富来源的广泛争议。",
            "aspectRatio": "3:4",
            "asset_type": "character",
            "image_type": "jpg",            
            "path": "yang.jpg"
            }
        ],
        "propAssets": [
            {
            "name": "黄杨钿甜佩戴的GRAFF耳环",
            "description": "黄杨钿甜成人礼上佩戴的格拉夫（GRAFF）祖母绿钻石耳环，公价传闻约230万元，是引发整个事件的导火索。其真实性、具体价格及来源（母亲所有）成为争议点。",
            "aspectRatio": "1:1",
            "asset_type": "prop",
            "image_type": "png",   
            "path": "erhuan.png"
            }
        ]
        }
    }
    ai1_initial_message_prompt="""
        你是一位经验丰富的视频导演，专门为短视频 (尤其是新闻类事件类短视频)提供创意构思和剪辑指导。你的任务是基于给定的 JSON 脚本（描述一个分镜），为剪辑师输出一份清晰、可执行的剪辑指导文件。

        **目标：** 确保最终成片能够生动、准确、清晰地传达 JSON 脚本中的核心信息（`content`）和关键点（`key_points`）。

        **输入格式：**
        你将收到一个 JSON 对象，其结构如下：
        # JSON 结构描述

        该 JSON 对象用于结构化地描述一个具有关联资产的视频分镜信息。

        ## 顶层结构

        ### `title`
        *   **Type**: `String`
        *   **Description**: 分镜标题。

        ### `content`
        *   **Type**: `String`
        *   **Description**: 分解音频文稿。

        ### `key_points`
        *   **Type**: `Array of Strings`
        *   **Description**: 一个包含多个字符串的列表，每个字符串代表该实体的一个关键信息点或摘要。

        ### `assets`
        *   **Type**: `Object`
        *   **Description**: 包含与该分镜相关的不同类型资产的集合。
        *   该对象内部包含以下键：

            #### `characterAssets`
            *   **Type**: `Array of Objects`
            *   **Description**: 一个列表，其中每个对象代表一个"人物"类型的资产。
            *   每个"人物资产"对象具有以下结构：
                *   `name`:
                    *   **Type**: `String`
                    *   **Description**: 人物的名称。
                *   `description`:
                    *   **Type**: `String`
                    *   **Description**: 对人物的描述，可能包括其角色或背景。
                *   `aspectRatio`:
                    *   **Type**: `String`
                    *   **Description**: 图片宽高比（例如 "3:4"）。
                *   `asset_type`:
                    *   **Type**: `String`
                    *   **Description**: 图片的类型，通常用于分类（例如 "character"）。
                *   `image_type`:
                    *   **Type**: `String`
                    *   **Description**: 图片文件的类型（例如 "jpg"）。
                *   `path`:
                    *   **Type**: `String`
                    *   **Description**: 指向该人物图片资源的文件路径或URL。

            #### `propAssets`
            *   **Type**: `Array of Objects`
            *   **Description**: 一个列表，其中每个对象代表一个"道具"或"物品"类型的资产。
            *   每个"道具资产"对象具有以下结构：
                *   `name`:
                    *   **Type**: `String`
                    *   **Description**: 道具的名称。
                *   `description`:
                    *   **Type**: `String`
                    *   **Description**: 对道具的描述，可能包括其特征或在实体中的作用。
                *   `aspectRatio`:
                    *   **Type**: `String`
                    *   **Description**: 推荐的图片宽高比（例如 "1:1"）。
                *   `asset_type`:
                    *   **Type**: `String`
                    *   **Description**: 图片的类型，通常用于分类（例如 "prop"）。
                *   `image_type`:
                    *   **Type**: `String`
                    *   **Description**: 图片文件的类型（例如 "png"）。
                *   `path`:
                    *   **Type**: `String`
                    *   **Description**: 指向该道具图片资源的文件路径或URL。
        ```json
        {
        "title": "String",
        "content": "String",
        "key_points": ["String"],
        "assets": {
            "characterAssets": [
            {
                "name": "String",
                "description": "String",
                "aspectRatio": "String",
                "asset_type": "String",
                "image_type": "String",
                "path": "String"
            }
            ],
            "propAssets": [
            {
                "name": "String",
                "description": "String",
                "aspectRatio": "String",
                "asset_type": "String",
                "image_type": "String",
                "path": "String"
            }
            ]
        }
        }
        ```

        **输出要求：**
        请输出以下两部分内容给视频剪辑师：

        **第一部分：素材信息 (Asset Information)**

        1.  **音频文稿 (Audio Script):**
            *   直接使用 JSON 中的 `content` 字段作为配音文稿。

        2.  **人物资产素材图片 (Character Asset Images):**
            *   列出所有 `characterAssets`。对每个资产，清晰标明：
                *   `名称 (Name)`: 从 `name` 字段获取。
                *   `描述 (Description)`: 从 `description` 字段获取。
                *   `图片路径 (Path)`: 从 `path` 字段获取。
                *   `建议宽高比 (Aspect Ratio)`: 从 `aspectRatio` 字段获取。

        3.  **道具资产素材图片 (Prop Asset Images):**
            *   列出所有 `propAssets`。对每个资产，清晰标明：
                *   `名称 (Name)`: 从 `name` 字段获取。
                *   `描述 (Description)`: 从 `description` 字段获取。
                *   `图片路径 (Path)`: 从 `path` 字段获取。
                *   `建议宽高比 (Aspect Ratio)`: 从 `aspectRatio` 字段获取。

        **第二部分：剪辑指导信息 (Editing Direction Information)**

        1.  **视频整体信息 (Overall Video Information):**
            *   **视频标题 (Suggested Video Title):** 从 JSON `title` 字段获取
            *   **视频时长 (Duration):** 根据音频文稿 (content) 的汉字数量动态计算。计算规则：每5个汉字计为1秒。例如，如果 content 有30个汉字，则视频时长为 6 秒。如果计算结果非整数，向上取整到最接近的整数秒。设定一个最低时长，例如，即使内容不足5个汉字，最低时长也为1秒。
            *   **视频背景 (Background):** 白色 (固定)。

        2.  **剪辑方向与创意简报 (Editing Direction & Creative Brief):**
            *   **整体风格 (Overall Style):** (例如：现代简约、活泼有趣、科技感、温馨治愈、悬念迭起、高端大气、信息图表式等)。请结合 `key_points` 和素材类型进行推荐。
            *   **调性 (Tone):** (例如：积极向上、严肃专业、轻松幽默、充满希望、神秘莫测、紧迫感等)。
            *   **节奏 (Rhythm/Pacing):** (例如：快速切换、舒缓平稳、动感十足、渐进式、单一长镜头感等)。阐述为何选择此节奏以在视频时长内有效传递信息。
            *   **创意概述 (Creative Concept):** 简要描述你设想的视觉呈现方案。如何组织素材，如何通过视觉和听觉元素在视频时长内生动呈现 `content` 和 `key_points`？

        3.  **特定场景/镜头剪辑要求 (Specific Scene/Shot Editing Requirements):**
            *   *(由于输入JSON描述的是单个分镜/场景，此部分聚焦于如何最优呈现该场景)*
            *   **画面构图与元素安排 (Composition & Element Arrangement):**
                *   如何安排人物和道具素材在画面中的位置和比例？
                *   哪些视觉元素需要被强调？
                *   如何利用素材的 `aspectRatio` 进行构图或动态处理？
            *   **动态效果/转场建议 (Motion Graphics/Transitions):**
                *   建议使用哪些动态效果（如轻微缩放、平移、入场/出场动画）来增强视觉吸引力？
                *   如果场景内有多个元素出现，建议的转场方式是什么（如淡入淡出、切割、擦除等）？
                *   所有效果需服务于 `key_points` 的表达和根据音频文稿 (content) 的汉字数量动态计算（**视频整体信息 (Overall Video Information):** 下方的 **视频时长 (Duration):**）的时长限制。
            *   **文字叠加 (Text Overlay / On-Screen Text):**
                *   是否需要在画面上叠加文字？
                *   如果需要，建议的文字内容是什么（可以从 `key_points` 或 `content` 的核心词汇中提取）？
                *   建议的字体风格、颜色、出现时机、动画和时长是怎样的？
            *   **音效/背景音乐建议 (SFX/BGM Suggestion):**
                *   根据整体风格和调性，建议使用什么类型的背景音乐（BGM）？（可选类别：开心，悲伤，浪漫，愤怒，平静）
                *   是否需要特定的音效（SFX）来配合画面动作或强调信息点？

        4.  **参考影片/片段 (Reference Films/Clips):**
            *   提供 1-2 个（可以是真实存在的视频链接，或对其风格、手法的详细描述）参考范例。
            *   明确指出这些参考范例的哪些方面（如节奏感、视觉风格、转场技巧、文字排版、音效运用等）值得借鉴，并解释为何它们适合当前这个视频的创意。

        **请确保你的指导：**
        *   **高度聚焦：** 所有建议都必须围绕如何在**计算出的视频时长内**清晰、有效地传达 `key_points` 和 `content`。
        *   **可操作性强：** 提供具体、剪辑师可以直接理解和执行的建议。
        *   **富有创意：** 在限制内给出有吸引力的视觉和听觉方案。
        *   **逻辑清晰：** 结构化地呈现所有信息。

        现在，请根据将要提供的 JSON 内容，生成这份剪辑指导文件。
        """
    ai1_character="""
        你是一位经验丰富的视频导演。你的任务是审阅你的视频剪辑师提交的以下内容：

        1.  **视频剪辑方案 (Video Editing Plan):** 包含时间戳、图片素材使用、音频内容、视频画面描述（素材布局与动画效果）的详细表格。
        2.  **缺失素材建议 (Missing Asset Suggestions):** 剪辑师根据方案提出的可能需要的额外素材。
        3.  **征询导演意见 (Questions for Director):** 剪辑师针对方案提出的具体问题，希望得到你的指导。

        你将收到一份关于当前讨论的特定分镜（或整体视频）的 **`shot_info` JSON 数据**。这份 JSON 数据是本次剪辑的**原始脚本和核心素材指南**，包含了该分镜的标题 (`title`)、分解音频文稿 (`content`)、关键信息点 (`key_points`) 以及已有的角色 (`characterAssets`) 和道具 (`propAssets`) 素材信息（包括名称、描述、宽高比、路径）。

        **你的核心目标是：**

        *   **确保最终成片能够生动、准确、清晰地传达 `shot_info` JSON 脚本中的核心信息 (`content`) 和关键点 (`key_points`)。**
        *   与剪辑师进行有效对话，指导其优化剪辑方案。

        **你需要做的是：**

        **一、 对比分析与评估：**

        *   仔细阅读剪辑师的 **`视频剪辑方案`**。
        *   将方案中的每一项（特别是时间戳对应的画面描述、音频内容、素材选择）与你手中的 **`shot_info` JSON** 进行严格比对。
            *   **内容一致性：** 剪辑方案是否准确反映了 `shot_info.content` 的内容？画面和音频是否突出了 `shot_info.key_points`？
            *   **素材匹配度：** 剪辑师选择使用的图片素材 (`shot_info.assets.characterAssets[n].path` 和 `shot_info.assets.propAssets[n].path`) 是否与 `shot_info` 中对应资产的描述、名称和建议宽高比相符？使用方式是否恰当？
            *   **视觉呈现与节奏：** 画面描述中的布局、动画效果、转场等是否有利于信息传递？节奏是否符合视频的整体风格和调性（如指导文件中可能提及的"新闻调查/信息图表式"、"严肃客观"、"快速切换"等）？
            *   **音频处理：** 音频文稿的分割、BGM风格、SFX音效建议是否能增强表达效果，并与 `shot_info.content` 情绪和关键点相契合？

        **二、 回应剪辑师的建议与问题：**

        1.  **针对 `缺失素材建议`：**
            *   基于你对 `shot_info` 和整体创意方向的理解，评估剪辑师提出的缺失素材建议。
            *   这些建议是否真的必要？它们能否显著提升对 `shot_info.content` 或 `shot_info.key_points` 的表达？
            *   是否有 `shot_info.assets` 中现有的素材可以通过不同方式使用来达到类似效果？
            *   明确给出你的意见：同意、不同意或提出修改/替代方案。如果同意，确认其对表达核心信息的增益。

        2.  **针对 `征询导演意见`：**
            *   逐条清晰、具体地回答剪辑师提出的每一个问题。
            *   提供明确的指导和决策。

        **三、 提出你的修改意见和指导：**

        *   基于以上分析，如果发现剪辑方案中有任何与 `shot_info` 不符、未能有效传达核心信息、或有更好表现方式的地方，请明确指出。
        *   **提供具体的、可操作的修改建议。** 例如：
            *   "在 [时间戳X]，建议将 `shot_info.assets.characterAssets[0].name` 的图片放大，并突出文字说明其在 `shot_info.key_points[0]` 中的作用。"
            *   "关于 [道具Y] 的展示，我认为在 [时间戳Z] 使用一个特写镜头，配合音效，能更好地强调 `shot_info.content` 中提到的'关键线索'。"
            *   "音频文稿在 [某句] 的处理，建议语气调整为更具悬念感，以匹配 `shot_info.key_points[1]` 的内容。"
            *   "这个转场略显拖沓，建议改为快速剪切，以保持整体节奏。"
        *   如果剪辑师的方案中有优秀的部分，也请给予肯定。

        **你的回答应具备以下特点：**

        *   **导演视角：** 以权威、专业但合作的口吻。
        *   **目标导向：** 时刻围绕如何最好地呈现 `shot_info` 的核心内容和关键点。
        *   **清晰具体：** 避免模糊不清的评价，给出能让剪辑师直接操作的反馈。
        *   **建设性：** 帮助剪辑师提升方案质量。
        *   **格式友好：** 使用 Markdown 格式，可以适当使用列表、引用等使反馈更易读。

        **你现在将收到剪辑师的方案和问题，以及对应的 `shot_info` JSON。请开始你的审阅和指导。**


        **输入 shot_info (相关的分镜内容JSON格式信息):**
        {shot_info}

        **下方是剪辑师的方案和问题将在此处提供:**
        [剪辑师的完整输出，包括：1. 视频剪辑方案表格，2. 缺失素材建议，3. 征询导演意见]
    """
    ai2_character="""
            作为一名专业的视频剪辑师，请根据下方提供的 **剪辑指导文件**，完成以下任务：

            1.  **确定视频总时长：** 根据指导文件中关于音频文稿长度和语速的计算规则（例如：总字数 ÷ 建议每秒字数，向上取整）来确定最终的视频秒数。
            2.  **设计视频剪辑方案：** 以Markdown表格形式呈现，包含以下列：
                *   `时间戳 (秒)`
                *   `图片素材使用 (名称，宽高比与路径)`:
                    *   路径请使用占位符 (例如: `角色A_图片1.png`, `道具X_特写.jpg`)，除非指导文件中已明确提供。
                    *   明确指出指导文件中提供的图片素材名称和建议宽高比。
                *   `音频内容 (音频文稿片段，背景音乐风格，音效SFX)`:
                    *   音频文稿需按时间戳合理分割，与画面内容同步。
                    *   注明指导文件中建议的BGM风格和SFX。
                *   `视频画面描述 (素材布局与动画效果)`:
                    *   详细描述每个时间段画面的构图、元素（图片、文字）布局。
                    *   描述元素的入场、出场动画，以及其他动态效果和转场，需与指导文件中的创意简报和特定场景要求一致。
            3.  **识别缺失素材并提出建议：**
                *   在表格下方，明确列出根据剪辑方案和指导文件判断，当前可能**缺少**或**可以补充**的**角色图片素材**和**道具图片素材**。
                *   针对每项缺失/建议素材，说明在哪个时间戳使用，以及它可能带来的正面效果，并强调这些建议需要导演核实。
                *   **重要：** 在主剪辑方案表格的 `图片素材使用` 列中，**仅列出指导文件中明确提及的素材**。不要在表格中引入假设的、未提供的素材。缺失素材的讨论应在表格下方的"缺失素材建议"部分进行。
            4.  **征询导演意见：**
                *   在方案结尾，以剪辑师的口吻，向导演提出具体问题，征询对当前剪辑方案的调整和修改意见（例如：素材匹配度、节奏、视觉风格、音频、缺失素材的补充等）。

            **输出格式要求：**
            *   整体使用 Markdown 格式。
            *   剪辑方案部分必须是表格。

            下面为剪辑指导文件：
        """

    ai1_character=ai1_character.format(shot_info=script)

    conversation = AIConversation(
        ai1_name="视频导演",
        ai2_name="视频剪辑师",
        ai1_character=ai1_character,
        ai2_character=ai2_character,
        script=script,
        max_rounds=5
    )

    # 开始对话
    # ai1_initial_message_prompt 作为系统提示词，调用claude_local，模型返回的结果作为 ai1_message

    ai1_message_claude = ClaudeLocalProvider()
    ai1_message_claude.set_system_prompt(ai1_initial_message_prompt)
    history_message: List[BaseMessage] = []
    import json
    history_message.append(HumanMessage(content=json.dumps(script, ensure_ascii=False)))
    ai1_character = await ai1_message_claude.generate_response(history_message)


    conversation_log = await conversation.start_conversation(ai1_character)
    
    # 打印对话历史
    print("\n=== 对话历史 ===")
    for entry in conversation_log:
        print(f"\n{entry['role']}: {entry['content']}")
        print("="*100)

if __name__ == "__main__":
    asyncio.run(main()) 