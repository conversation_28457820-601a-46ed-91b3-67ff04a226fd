from manim import *
import os
from layout_manager import LayoutManager

'''
========== 动画素材清单 ==========

## 图片素材：
- 杨伟灰色剪影: [使用Manim内置图形] - 代表杨伟公职人员
- 深圳市国影影视文化传播有限公司: image.prop - 杨伟关联的影视公司
- 益善堂（深圳）生物科技有限公司: image.prop - 杨伟关联的生物科技公司

## 文字关键词素材：
- "经商乱象" - 0s-12s - 黑体24pt加粗黑色
- "杨伟" - 0.5s-12s - 黑体18pt黑色
- "公职人员" - 1s-12s - 黑体18pt斜体黑色
- "深圳国影影视公司" - 3s-12s - 黑体18pt黑色
- "2014年" - 3s-12s - 黑体18pt橙色
- "注册资本：" - 3.5s-12s - 黑体16pt黑色
- "500万" - 4s-12s - 黑体20pt加粗红色
- "益善堂生物科技" - 6s-12s - 黑体18pt黑色
- "2020年" - 6s-12s - 黑体18pt橙色
- "疫情初期" - 6.5s-12s - 黑体16pt斜体黄色
- "注册资本：" - 6.5s-12s - 黑体16pt黑色
- "1000万" - 7s-12s - 黑体20pt加粗红色
- "亲属代持" - 9s-12s - 黑体20pt加粗红色
- "规避监管" - 9.5s-12s - 黑体20pt加粗红色
- "公职身份高度重合" - 10s-12s - 黑体18pt黑色
- "事发后紧急变更信息" - 10.5s-12s - 黑体18pt黑色(黄底)

## 动画总时长：12秒
=====================================
'''

class EconomicIrregularities(Scene):
    def construct(self):
        # 设置背景色
        self.camera.background_color = "#FFFFFF"
        
        # 检查图片资源是否存在
        image_path = "image.prop"
        if not os.path.exists(image_path):
            print(f"警告: 图片文件 {image_path} 不存在，将使用占位图形")
            use_placeholder = True
        else:
            use_placeholder = False
        
        # 创建时间轴（2014-2020年）
        timeline = Line(LEFT * 5, RIGHT * 5, color=GRAY)
        timeline.to_edge(UP, buff=2)
        
        timeline_start = Text("2014", font="SimHei", font_size=16, color=GRAY)
        timeline_start.next_to(timeline.get_start(), DOWN, buff=0.2)
        
        timeline_end = Text("2020", font="SimHei", font_size=16, color=GRAY)
        timeline_end.next_to(timeline.get_end(), DOWN, buff=0.2)
        
        # 创建时间节点
        node_2014 = Dot(timeline.get_start(), color=ORANGE)
        node_2020 = Dot(timeline.get_end(), color=ORANGE)
        
        # 创建主标题
        title = Text("经商乱象", font="SimHei", font_size=24, weight=BOLD, color=BLACK)
        title.to_edge(UP, buff=0.5)
        title.set_opacity(0)
        
        # 创建杨伟剪影
        yang_wei = SVGMobject("person_silhouette", fill_opacity=1, fill_color=GRAY)
        if not os.path.exists("person_silhouette"):
            # 如果SVG不存在，使用简单形状代替
            yang_wei = VGroup(
                Circle(radius=0.4, fill_opacity=1, color=GRAY),
                Rectangle(height=1.2, width=0.8, fill_opacity=1, color=GRAY).next_to(Circle(radius=0.4), DOWN, buff=0)
            )
        
        yang_wei.height = 2
        yang_wei.move_to([3, 0, 0])
        yang_wei.set_opacity(0)
        
        # 创建杨伟标签
        yang_wei_label = Text("杨伟", font="SimHei", font_size=18, color=BLACK)
        yang_wei_label.next_to(yang_wei, UP, buff=0.2)
        yang_wei_label.shift(RIGHT * 0.5)
        yang_wei_label.set_opacity(0)
        
        # 创建公职人员标签
        official_label = Text("公职人员", font="SimHei", font_size=18, slant=ITALIC, color=BLACK)
        official_label.next_to(yang_wei_label, DOWN, buff=0.2)
        official_label.set_opacity(0)
        
        # 创建深圳国影影视公司
        if use_placeholder:
            company1 = Rectangle(height=2.8, width=5, fill_opacity=0.2, color=BLUE)
        else:
            company1 = ImageMobject(image_path)
            company1.height = 2.8
            company1.width = 5  # 宽高比1.78
        
        company1.move_to([-3.5, 0, 0])
        company1.set_opacity(0)
        
        # 创建深圳国影影视公司标签
        company1_label = Text("深圳国影影视公司", font="SimHei", font_size=18, color=BLACK)
        company1_label.next_to(company1, UP, buff=0.2)
        company1_label.set_opacity(0)
        
        # 创建2014年标签
        year2014_label = Text("2014年", font="SimHei", font_size=18, color=ORANGE)
        year2014_label.move_to([-5.5, 2, 0])
        year2014_label.set_opacity(0)
        
        # 创建注册资本标签1
        capital_label1 = Text("注册资本：", font="SimHei", font_size=16, color=BLACK)
        capital_label1.move_to([-4.5, -1, 0])
        capital_label1.set_opacity(0)
        
        # 创建500万标签
        capital_amount1 = Text("500万", font="SimHei", font_size=20, weight=BOLD, color=RED)
        capital_amount1.next_to(capital_label1, RIGHT, buff=0.2)
        capital_amount1.set_opacity(0)
        
        # 创建益善堂生物科技公司
        if use_placeholder:
            company2 = Rectangle(height=2.8, width=5, fill_opacity=0.2, color=GREEN)
        else:
            company2 = ImageMobject(image_path)
            company2.height = 2.8
            company2.width = 5  # 宽高比1.78
        
        company2.move_to([3.5, 0, 0])
        company2.set_opacity(0)
        
        # 创建益善堂生物科技标签
        company2_label = Text("益善堂生物科技", font="SimHei", font_size=18, color=BLACK)
        company2_label.next_to(company2, UP, buff=0.2)
        company2_label.set_opacity(0)
        
        # 创建2020年标签
        year2020_label = Text("2020年", font="SimHei", font_size=18, color=ORANGE)
        year2020_label.move_to([1.5, 2, 0])
        year2020_label.set_opacity(0)
        
        # 创建疫情初期标签
        pandemic_label = Text("疫情初期", font="SimHei", font_size=16, slant=ITALIC, color=YELLOW)
        pandemic_label.next_to(year2020_label, RIGHT, buff=0.2)
        pandemic_label.set_opacity(0)
        
        # 创建注册资本标签2
        capital_label2 = Text("注册资本：", font="SimHei", font_size=16, color=BLACK)
        capital_label2.move_to([2.5, -1, 0])
        capital_label2.set_opacity(0)
        
        # 创建1000万标签
        capital_amount2 = Text("1000万", font="SimHei", font_size=20, weight=BOLD, color=RED)
        capital_amount2.next_to(capital_label2, RIGHT, buff=0.2)
        capital_amount2.set_opacity(0)
        
        # 创建亲属代持标签
        relative_holding = Text("亲属代持", font="SimHei", font_size=20, weight=BOLD, color=RED)
        relative_holding.move_to([0, 2, 0])
        relative_holding.set_opacity(0)
        
        # 创建规避监管标签
        avoid_regulation = Text("规避监管", font="SimHei", font_size=20, weight=BOLD, color=RED)
        avoid_regulation.move_to([0, 0, 0])
        avoid_regulation.set_opacity(0)
        
        # 创建公职身份高度重合标签
        overlap_label = Text("公职身份高度重合", font="SimHei", font_size=18, color=BLACK)
        overlap_label.move_to([0, -2, 0])
        overlap_label.set_opacity(0)
        
        # 创建事发后紧急变更信息标签
        emergency_change = Text("事发后紧急变更信息", font="SimHei", font_size=18, color=BLACK)
        emergency_change_bg = Rectangle(height=emergency_change.height + 0.2, 
                                       width=emergency_change.width + 0.4, 
                                       fill_opacity=1, 
                                       color=YELLOW)
        emergency_change_bg.move_to([0, -3.5, 0])
        emergency_change.move_to([0, -3.5, 0])
        emergency_change_bg.set_opacity(0)
        emergency_change.set_opacity(0)
        
        # 创建连接线
        line1 = DashedLine(yang_wei.get_left(), company1.get_right(), color=RED)
        line2 = DashedLine(yang_wei.get_left(), company2.get_right(), color=RED)
        line1.set_opacity(0)
        line2.set_opacity(0)
        
        # 确保布局安全
        LayoutManager.ensure_screen_bounds(title)
        LayoutManager.ensure_screen_bounds(yang_wei)
        LayoutManager.ensure_screen_bounds(company1)
        LayoutManager.ensure_screen_bounds(company2)
        
        # 第一阶段 (0.0s ~ 3.0s): 杨伟任职期间，家族多家公司相继成立
        # 主标题滑入
        self.play(
            FadeIn(title),
            run_time=0.5
        )
        
        # 杨伟剪影出现
        self.play(
            FadeIn(yang_wei),
            run_time=0.5
        )
        
        # 杨伟标签出现
        self.play(
            FadeIn(yang_wei_label),
            run_time=0.5
        )
        
        # 公职人员标签出现
        self.play(
            FadeIn(official_label),
            run_time=0.5
        )
        
        # 时间轴出现
        self.play(
            Create(timeline),
            FadeIn(timeline_start),
            FadeIn(timeline_end),
            FadeIn(node_2014),
            FadeIn(node_2020),
            run_time=1.0
        )
        
        # 第二阶段 (3.0s ~ 6.0s): 深圳国影影视公司
        # 2014年节点高亮
        self.play(
            node_2014.animate.scale(1.5).set_color(ORANGE),
            run_time=0.5
        )
        
        # 深圳国影影视公司出现
        self.play(
            FadeIn(company1),
            FadeIn(company1_label),
            FadeIn(year2014_label),
            run_time=1.0
        )
        
        # 连接线出现
        self.play(
            Create(line1),
            run_time=0.5
        )
        
        # 注册资本标签出现
        self.play(
            FadeIn(capital_label1),
            run_time=0.5
        )
        
        # 500万资本金动态计数效果
        self.play(
            FadeIn(capital_amount1),
            run_time=0.5
        )
        
        # 第三阶段 (6.0s ~ 9.0s): 益善堂生物科技
        # 2020年节点高亮
        self.play(
            node_2020.animate.scale(1.5).set_color(ORANGE),
            run_time=0.5
        )
        
        # 益善堂生物科技公司出现
        self.play(
            FadeIn(company2),
            FadeIn(company2_label),
            FadeIn(year2020_label),
            run_time=1.0
        )
        
        # 疫情初期标签出现
        self.play(
            FadeIn(pandemic_label),
            run_time=0.5
        )
        
        # 连接线出现
        self.play(
            Create(line2),
            run_time=0.5
        )
        
        # 注册资本标签出现
        self.play(
            FadeIn(capital_label2),
            run_time=0.5
        )
        
        # 1000万资本金动态计数效果
        self.play(
            FadeIn(capital_amount2),
            run_time=0.5
        )
        
        # 第四阶段 (9.0s ~ 12.0s): 揭示关联和变更
        # 公司图片变暗
        self.play(
            company1.animate.set_opacity(0.7),
            company2.animate.set_opacity(0.7),
            run_time=0.5
        )
        
        # 亲属代持标签出现
        self.play(
            FadeIn(relative_holding),
            run_time=0.5
        )
        
        # 规避监管标签出现
        self.play(
            FadeIn(avoid_regulation),
            run_time=0.5
        )
        
        # 公职身份高度重合标签出现
        self.play(
            FadeIn(overlap_label),
            run_time=0.5
        )
        
        # 事发后紧急变更信息标签出现
        self.play(
            FadeIn(emergency_change_bg),
            FadeIn(emergency_change),
            run_time=0.5
        )
        
        # 结尾强调效果
        self.play(
            Flash(relative_holding),
            Flash(avoid_regulation),
            run_time=0.5
        )
        
        # 等待最后一秒
        self.wait(0.5)