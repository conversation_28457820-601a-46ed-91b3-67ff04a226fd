from manim import *
import os
from layout_manager import LayoutManager

'''
========== 动画素材清单 ==========

## 图片素材：
- 黄杨钿甜: character.jpg - 事件的核心人物，童星，因在成人礼佩戴高价耳环引发对其家庭背景和财富来源的广泛争议
- GRAFF耳环: prop.jpg - 黄杨钿甜成人礼上佩戴的格拉夫（GRAFF）祖母绿钻石耳环，公价传闻约230万元，是引发整个事件的导火索

## 文字关键词素材：
- "2025年5月11日" - 0s-3s - 屏幕上方中央 (0, 3)，无衬线字体，32px，黑色
- "耳环之谜"(水印) - 0s-12s - 右下角 (6, -3.5)，半透明细字体，24px，半透明灰色
- "GRAFF 祖母绿钻石耳环" - 3s-6s - 耳环旁边，粗体无衬线字体，28px，黑色
- "¥2,300,000" - 6s-9s - 屏幕中下方 (0, -2)，大号粗体数字，48px，红色或黑色
- ""耳环是找妈妈的"" - 9s-12s - 屏幕中央 (0, 0)，斜体，36px，黑色
- "引发网友质疑" - 9s-12s - 屏幕底部 (0, -3)，无衬线字体，32px，深红色或黑色

## 动画总时长：12秒
## 画布尺寸：14个宽度单位 × 8个高度单位，坐标系原点：画布中心点(0,0)
=====================================
'''

class EarringMysteryAnimation(Scene):
    def construct(self):
        # 设置背景色为白色（根据设计文档）
        self.camera.background_color = "#FFFFFF"

        # 检查图片资源是否存在 - 使用绝对路径
        import os
        script_dir = os.path.dirname(os.path.abspath(__file__))
        character_path = os.path.join(script_dir, "hydt.jpg")
        earring_path = os.path.join(script_dir, "yhdt_earring.jpg")

        for path in [character_path, earring_path]:
            if not os.path.exists(path):
                print(f"警告: 文件 {path} 不存在!")
            else:
                print(f"成功找到图片文件: {path}")

        # 创建水印（全程显示0s-12s）
        watermark = Text("耳环之谜", font="SimHei", font_size=24, color=GREY_C)
        watermark.move_to([6, -3.5, 0])  # 右下角位置
        self.add(watermark)  # 直接添加水印，不使用动画

        # ===== 第一阶段 (0s-3s): 黄杨钿甜照片和日期 =====

        # 创建黄杨钿甜照片
        character = ImageMobject(character_path)
        # 根据宽高比0.75设置尺寸：4x5.33单位
        character.height = 5.33
        character.width = 4
        character.move_to([0, 0, 0])  # 居中放置

        # 创建日期文本
        date_text = Text("2025年5月11日", font="SimHei", font_size=32, color=BLACK)
        date_text.move_to([0, 3, 0])  # 屏幕上方中央

        # 动画：照片和日期淡入，模拟社交媒体加载效果
        self.play(
            FadeIn(character, scale=1.2),  # 照片从小到大缓慢淡入
            FadeIn(date_text),
            run_time=1.5
        )

        # 确保元素不重叠且在屏幕边界内
        LayoutManager.ensure_screen_bounds(character)
        LayoutManager.ensure_screen_bounds(date_text)
        LayoutManager.print_layout_debug(character, "黄杨钿甜照片")
        LayoutManager.print_layout_debug(date_text, "日期文本")

        # 等待，确保第一阶段总时长为3秒
        self.wait(1.5)

        # ===== 第二阶段 (3s-6s): 耳环特写和标签 =====

        # 镜头平滑放大至黄杨钿甜耳朵部位
        character_closeup = ImageMobject(character_path)
        character_closeup.height = 8
        character_closeup.width = 6
        character_closeup.move_to([0, 0, 0])

        # 创建耳环标签
        earring_label = Text("GRAFF 祖母绿钻石耳环", font="SimHei", font_size=28, color=BLACK, weight=BOLD)
        earring_label.move_to([3, 1, 0])  # 耳环旁边

        # 动画：日期淡出，照片放大至耳朵部位，显示耳环标签
        self.play(
            FadeOut(date_text),
            Transform(character, character_closeup),
            run_time=1.5
        )

        # 添加轻微的放大镜效果
        self.play(
            FadeIn(earring_label),
            run_time=1
        )

        # 确保元素不重叠且在屏幕边界内
        LayoutManager.ensure_screen_bounds(character)
        LayoutManager.ensure_screen_bounds(earring_label)
        LayoutManager.print_layout_debug(character, "黄杨钿甜特写")
        LayoutManager.print_layout_debug(earring_label, "耳环标签")

        # 等待，确保第二阶段总时长为3秒
        self.wait(0.5)

        # ===== 第三阶段 (6s-9s): 耳环特写和价格 =====

        # 创建耳环图片
        earring = ImageMobject(earring_path)
        # 根据宽高比1.0设置尺寸：5x5单位
        earring.height = 5
        earring.width = 5
        earring.move_to([3, 0, 0])  # 右侧位置

        # 调整人物照片位置到左侧
        character_side = ImageMobject(character_path)
        character_side.height = 5.33
        character_side.width = 4
        character_side.move_to([-4, 0, 0])  # 左侧位置

        # 动画：转换到分屏布局，耳环特写图片从右侧滑入
        self.play(
            FadeOut(earring_label),
            Transform(character, character_side),
            FadeIn(earring, shift=RIGHT*3),
            run_time=1.5
        )

        # 价格数字从0增长到2,300,000的动画
        price_counter = DecimalNumber(
            0,
            show_ellipsis=False,
            num_decimal_places=0,
            font_size=48,
            color=RED,
            include_sign=False,
        )
        price_counter.move_to([0, -2, 0])  # 屏幕中下方

        yuan_symbol = Text("¥", font_size=48, color=RED).next_to(price_counter, LEFT, buff=0.1)

        self.play(
            FadeIn(yuan_symbol),
            FadeIn(price_counter),
            run_time=0.5
        )

        # 价格标签以数字快速增长动画方式呈现
        self.play(
            ChangeDecimalToValue(price_counter, 2300000),
            run_time=1.5
        )

        # 确保元素不重叠且在屏幕边界内
        LayoutManager.ensure_screen_bounds(character)
        LayoutManager.ensure_screen_bounds(earring)
        LayoutManager.ensure_screen_bounds(price_counter)
        LayoutManager.print_layout_debug(character, "黄杨钿甜侧面")
        LayoutManager.print_layout_debug(earring, "耳环特写")
        LayoutManager.print_layout_debug(price_counter, "价格标签")

        # 等待，确保第三阶段总时长为3秒
        self.wait(0.5)

        # ===== 第四阶段 (9s-12s): 引发质疑 =====

        # 创建引用文字
        quote_text = Text('"耳环是找妈妈的"', font="SimHei", font_size=36, color=BLACK, slant=ITALIC)
        quote_text.move_to([0, 0, 0])  # 屏幕中央

        # 创建质疑文字
        doubt_text = Text("引发网友质疑", font="SimHei", font_size=32, color=RED_E)
        doubt_text.move_to([0, -3, 0])  # 屏幕底部

        # 创建问号图标组，呈现漩涡状动画
        question_marks = VGroup()
        for i in range(8):
            angle = i * PI / 4
            radius = 2
            question_mark = Text("?", font_size=36, color=GREY)
            question_mark.move_to([radius * np.cos(angle), radius * np.sin(angle), 0])
            question_marks.add(question_mark)

        # 动画：价格淡出，显示引用文字
        self.play(
            FadeOut(yuan_symbol),
            FadeOut(price_counter),
            FadeIn(quote_text),
            run_time=1
        )

        # 显示质疑文字和问号旋转出现
        self.play(
            FadeIn(doubt_text),
            FadeIn(question_marks, lag_ratio=0.1),
            run_time=1
        )

        # 问号旋转动画，表现争议和质疑氛围
        self.play(
            Rotating(question_marks, about_point=ORIGIN, angle=PI/2, run_time=1),
            question_marks.animate.scale(1.2)
        )

        # 确保元素不重叠且在屏幕边界内
        LayoutManager.ensure_screen_bounds(quote_text)
        LayoutManager.ensure_screen_bounds(doubt_text)
        LayoutManager.print_layout_debug(quote_text, "引用文字")
        LayoutManager.print_layout_debug(doubt_text, "质疑文字")

        # 等待，确保第四阶段总时长为3秒
        self.wait(0.1)
