<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>黄杨钿甜GRAFF耳环争议动画</title>
    <style>
        /* 全局样式与画布设置 */
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            width: 100vw;
            height: 100vh;
            background-color: #FFFFFF;
            overflow: hidden;
            font-family: 'Microsoft YaHei', 'SimHei', Arial, sans-serif;
        }

        /* 动画容器 - 维持16:9比例 */
        .animation-container {
            width: 100vw;
            height: 100vh;
            position: relative;
            background-color: #FFFFFF;
        }

        /* 坐标系转换基础类 */
        .element {
            position: absolute;
            left: 50%;
            top: 50%;
            transform-origin: center center;
        }

        /* 人物元素样式 */
        .character {
            border-radius: 8px;
            box-shadow: 0 4px 16px rgba(0,0,0,0.1);
            object-fit: cover;
            transition: all 0.8s cubic-bezier(0.4, 0, 0.2, 1);
        }

        /* 道具元素样式 */
        .prop {
            border-radius: 12px;
            box-shadow: 0 8px 24px rgba(0,0,0,0.15);
            object-fit: contain;
            transition: all 0.8s cubic-bezier(0.4, 0, 0.2, 1);
        }

        /* 文字元素基础样式 */
        .text-element {
            white-space: nowrap;
            text-align: center;
            font-weight: bold;
            transition: all 0.6s ease-in-out;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.1);
        }

        /* 特定文字样式 */
        .title-text {
            font-family: 'SimHei', 'Microsoft YaHei', serif;
            font-size: 3.4vw;
            color: #000000;
            font-weight: 900;
        }

        .product-text {
            font-family: Arial, 'Microsoft YaHei', sans-serif;
            font-size: 2.3vw;
            color: #000000;
            font-weight: 500;
        }

        .price-text {
            font-family: 'Arial Black', 'SimHei', monospace;
            font-size: 4.6vw;
            color: #FF0000;
            font-weight: 900;
            text-shadow: 3px 3px 6px rgba(255,0,0,0.3);
        }

        .quote-text {
            font-family: 'KaiTi', 'SimKai', serif;
            font-size: 2.6vw;
            color: #4A90E2;
            font-style: italic;
            font-weight: 600;
        }

        .identity-text {
            font-family: 'SimHei', 'Microsoft YaHei', serif;
            font-size: 2.3vw;
            color: #8B4513;
            font-weight: 700;
        }

        .vs-text {
            font-family: 'Arial Black', 'SimHei', sans-serif;
            font-size: 4.0vw;
            color: #000000;
            font-weight: 900;
            text-shadow: 4px 4px 8px rgba(0,0,0,0.2);
        }

        .question-text {
            font-family: 'SimHei', 'Microsoft YaHei', serif;
            font-size: 3.4vw;
            color: #000000;
            font-weight: 900;
        }

        .question-mark {
            font-size: 5.1vw;
            animation: pulse-question 1s infinite ease-in-out;
        }

        /* 动画效果定义 */
        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translate(calc(-50% + var(--x-offset)), calc(-50% + var(--y-offset) + 5vh));
            }
            to {
                opacity: 1;
                transform: translate(calc(-50% + var(--x-offset)), calc(-50% + var(--y-offset)));
            }
        }

        @keyframes fadeInScale {
            from {
                opacity: 0;
                transform: translate(calc(-50% + var(--x-offset)), calc(-50% + var(--y-offset))) scale(0.3);
            }
            to {
                opacity: 1;
                transform: translate(calc(-50% + var(--x-offset)), calc(-50% + var(--y-offset))) scale(1);
            }
        }

        @keyframes slideInLeft {
            from {
                opacity: 0;
                transform: translate(calc(-50% + var(--x-offset) - 20vw), calc(-50% + var(--y-offset)));
            }
            to {
                opacity: 1;
                transform: translate(calc(-50% + var(--x-offset)), calc(-50% + var(--y-offset)));
            }
        }

        @keyframes slideInRight {
            from {
                opacity: 0;
                transform: translate(calc(-50% + var(--x-offset) + 20vw), calc(-50% + var(--y-offset)));
            }
            to {
                opacity: 1;
                transform: translate(calc(-50% + var(--x-offset)), calc(-50% + var(--y-offset)));
            }
        }

        @keyframes rotateEntry {
            from {
                opacity: 0;
                transform: translate(calc(-50% + var(--x-offset)), calc(-50% + var(--y-offset))) scale(0.2) rotate(180deg);
            }
            to {
                opacity: 1;
                transform: translate(calc(-50% + var(--x-offset)), calc(-50% + var(--y-offset))) scale(1) rotate(0deg);
            }
        }

        @keyframes countUp {
            0% { transform: translate(calc(-50% + var(--x-offset)), calc(-50% + var(--y-offset))) scale(0.5); }
            50% { transform: translate(calc(-50% + var(--x-offset)), calc(-50% + var(--y-offset))) scale(1.2); }
            100% { transform: translate(calc(-50% + var(--x-offset)), calc(-50% + var(--y-offset))) scale(1); }
        }

        @keyframes typewriter {
            from { width: 0; }
            to { width: 100%; }
        }

        @keyframes pulse-question {
            0%, 100% { transform: translate(calc(-50% + var(--x-offset)), calc(-50% + var(--y-offset))) scale(1); }
            50% { transform: translate(calc(-50% + var(--x-offset)), calc(-50% + var(--y-offset))) scale(1.1); }
        }

        @keyframes shake {
            0%, 100% { transform: translate(calc(-50% + var(--x-offset)), calc(-50% + var(--y-offset))) rotate(0deg); }
            25% { transform: translate(calc(-50% + var(--x-offset)), calc(-50% + var(--y-offset))) rotate(-1deg); }
            75% { transform: translate(calc(-50% + var(--x-offset)), calc(-50% + var(--y-offset))) rotate(1deg); }
        }

        /* 隐藏类 */
        .hidden {
            opacity: 0;
            pointer-events: none;
        }

        /* 打字机效果容器 */
        .typewriter-container {
            overflow: hidden;
            white-space: nowrap;
            border-right: 2px solid transparent;
        }

        .typewriter-active {
            animation: typewriter 2s steps(30, end);
            border-right: 2px solid #4A90E2;
        }
    </style>
</head>
<body>
    <div class="animation-container">
        <!-- 主要人物：黄杨钿甜 -->
        <img id="yhdt" class="element character hidden" 
             src="../data/assets/characters/yhdt.jpg" 
             alt="黄杨钿甜"
             style="--x-offset: -14.28vw; --y-offset: 0vh; width: 42.84vw; height: 100vh; object-fit: cover;">

        <!-- 人物：黄杨钿甜父亲 -->
        <img id="yhdt-father" class="element character hidden" 
             src="../data/assets/characters/yhdt_f.jpg" 
             alt="黄杨钿甜父亲"
             style="--x-offset: -28.56vw; --y-offset: 0vh; width: 28.56vw; height: 66.25vh; object-fit: cover;">

        <!-- 道具：GRAFF耳环 -->
        <img id="graff-earring" class="element prop hidden" 
             src="../data/assets/props/yhdt_earring.jpg" 
             alt="GRAFF耳环"
             style="--x-offset: 21.42vw; --y-offset: 0vh; width: 28.56vw; height: 50vh;">

        <!-- 文字元素 -->
        <!-- 标题：引爆点 -->
        <div id="title" class="element text-element title-text hidden"
             style="--x-offset: -42.84vw; --y-offset: -43.75vh;">
            引爆点
        </div>

        <!-- 产品名称 -->
        <div id="product-name" class="element text-element product-text hidden"
             style="--x-offset: 35.7vw; --y-offset: 37.5vh;">
            GRAFF祖母绿钻石耳环
        </div>

        <!-- 价格 -->
        <div id="price" class="element text-element price-text hidden"
             style="--x-offset: 0vw; --y-offset: -12.5vh;">
            <span id="price-number">¥0</span>万
        </div>

        <!-- 引用文字 -->
        <div id="quote" class="element text-element quote-text typewriter-container hidden"
             style="--x-offset: 0vw; --y-offset: 25vh;">
            "耳环是找妈妈的"
        </div>

        <!-- 前公务员身份 -->
        <div id="identity" class="element text-element identity-text hidden"
             style="--x-offset: -28.56vw; --y-offset: 25vh;">
            前公务员身份
        </div>

        <!-- VS对比符号 -->
        <div id="vs" class="element text-element vs-text hidden"
             style="--x-offset: 0vw; --y-offset: 0vh;">
            VS
        </div>

        <!-- 财富来源质疑 -->
        <div id="question" class="element text-element question-text hidden"
             style="--x-offset: 0vw; --y-offset: 37.5vh;">
            财富来源<span class="question-mark">?</span>
        </div>
    </div>

    <script>
        // 基础配置
        const BASE_UNIT_VW = 100 / 14; // 7.14vw
        const BASE_UNIT_VH = 100 / 8;  // 12.5vh

        // 获取元素引用
        const elements = {
            yhdt: document.getElementById('yhdt'),
            yhdtFather: document.getElementById('yhdt-father'),
            graffEarring: document.getElementById('graff-earring'),
            title: document.getElementById('title'),
            productName: document.getElementById('product-name'),
            price: document.getElementById('price'),
            priceNumber: document.getElementById('price-number'),
            quote: document.getElementById('quote'),
            identity: document.getElementById('identity'),
            vs: document.getElementById('vs'),
            question: document.getElementById('question')
        };

        // 显示元素函数
        function showElement(element, animationClass = 'fadeInScale', duration = 800) {
            element.classList.remove('hidden');
            element.style.animation = `${animationClass} ${duration}ms cubic-bezier(0.4, 0, 0.2, 1) forwards`;
        }

        // 隐藏元素函数
        function hideElement(element, duration = 600) {
            element.style.animation = `fadeOut ${duration}ms ease-in-out forwards`;
            setTimeout(() => {
                element.classList.add('hidden');
            }, duration);
        }

        // 更新元素位置和尺寸
        function updateElement(element, x, y, width, height) {
            element.style.setProperty('--x-offset', `${x * BASE_UNIT_VW}vw`);
            element.style.setProperty('--y-offset', `${y * BASE_UNIT_VH}vh`);
            if (width) element.style.width = `${width * BASE_UNIT_VW}vw`;
            if (height) element.style.height = `${height * BASE_UNIT_VH}vh`;
            element.style.transform = `translate(calc(-50% + var(--x-offset)), calc(-50% + var(--y-offset)))`;
        }

        // 价格计数动画
        function animatePrice() {
            let current = 0;
            const target = 230;
            const duration = 1500;
            const steps = 30;
            const increment = target / steps;
            const stepDuration = duration / steps;

            const interval = setInterval(() => {
                current += increment;
                if (current >= target) {
                    current = target;
                    clearInterval(interval);
                    // 音效提示: 计数结束音效应在此处播放
                }
                elements.priceNumber.textContent = `¥${Math.round(current)}`;
            }, stepDuration);

            // 数字跳动效果
            elements.price.style.animation = 'countUp 1.5s ease-out forwards';
        }

        // 打字机效果
        function typewriterEffect(element) {
            element.classList.add('typewriter-active');
            // 音效提示: 打字音效应在此处播放
            setTimeout(() => {
                element.style.borderRight = 'none';
            }, 2000);
        }

        // 问号脉冲效果
        function questionPulse() {
            const questionMark = elements.question.querySelector('.question-mark');
            questionMark.style.animation = 'pulse-question 1s infinite ease-in-out';
            // 音效提示: 悬疑音效应在此处播放
        }

        // 震动效果
        function shakeEffect(element) {
            element.style.animation = 'shake 0.5s ease-in-out 3';
            // 音效提示: 不和谐音效应在此处播放
        }

        // 时间轴控制函数
        function initTimeline() {
            // 0s - 2s: 黄杨钿甜照片入场 + 标题
            setTimeout(() => {
                updateElement(elements.yhdt, -2, 0, 6, 8);
                showElement(elements.yhdt, 'fadeInScale', 1000);
                showElement(elements.title, 'slideInLeft', 800);
                // 音效提示: 闪光音效应在此处播放
            }, 0);

            // 2s - 4s: 缩小黄杨钿甜，耳环入场 + 产品名称
            setTimeout(() => {
                updateElement(elements.yhdt, -4, 0, 3, 4);
                elements.yhdt.style.transition = 'all 0.8s cubic-bezier(0.4, 0, 0.2, 1)';
                
                updateElement(elements.graffEarring, 3, 0, 4, 4);
                showElement(elements.graffEarring, 'rotateEntry', 1200);
                showElement(elements.productName, 'fadeInUp', 800);
                // 音效提示: 闪光音效应在此处播放
            }, 2000);

            // 4s - 6s: 进一步缩小元素，价格突出显示
            setTimeout(() => {
                updateElement(elements.yhdt, -6, 2, 2, 2.7);
                updateElement(elements.graffEarring, 6, 2, 2, 2);
                elements.yhdt.style.transition = 'all 0.8s cubic-bezier(0.4, 0, 0.2, 1)';
                elements.graffEarring.style.transition = 'all 0.8s cubic-bezier(0.4, 0, 0.2, 1)';
                
                hideElement(elements.productName);
                showElement(elements.price, 'fadeInScale', 1000);
                
                setTimeout(() => {
                    animatePrice();
                }, 300);
            }, 4000);

            // 6s - 8s: 引用文字出现
            setTimeout(() => {
                updateElement(elements.yhdt, -6, 0, 2, 2.7);
                updateElement(elements.graffEarring, 6, 0, 2, 2);
                updateElement(elements.price, 0, -2, null, null);
                elements.price.style.fontSize = '3.4vw';
                
                showElement(elements.quote, 'fadeInUp', 800);
                setTimeout(() => {
                    typewriterEffect(elements.quote);
                }, 400);
            }, 6000);

            // 8s - 10s: 父亲照片替换黄杨钿甜，VS对比
            setTimeout(() => {
                hideElement(elements.yhdt);
                hideElement(elements.quote);
                
                updateElement(elements.yhdtFather, -4, 0, 4, 5.3);
                updateElement(elements.graffEarring, 4, 0, 4, 4);
                
                showElement(elements.yhdtFather, 'slideInLeft', 1000);
                showElement(elements.identity, 'fadeInUp', 800);
                showElement(elements.vs, 'fadeInScale', 600);
                
                setTimeout(() => {
                    shakeEffect(elements.vs);
                }, 800);
            }, 8000);

            // 10s - 12s: 最终质疑文字出现
            setTimeout(() => {
                updateElement(elements.yhdtFather, -4, 1, 3, 4);
                updateElement(elements.graffEarring, 4, 1, 3, 3);
                updateElement(elements.vs, 0, 1, null, null);
                updateElement(elements.identity, -4, -0.5, null, null);
                elements.vs.style.fontSize = '2.8vw';
                elements.identity.style.fontSize = '1.8vw';
                
                showElement(elements.question, 'fadeInUp', 1000);
                
                setTimeout(() => {
                    questionPulse();
                }, 500);
            }, 10000);
        }

        // 确保所有图片加载完成后开始动画
        function preloadImages() {
            const images = [
                '../data/assets/characters/yhdt.jpg',
                '../data/assets/characters/yhdt_f.jpg',
                '../data/assets/props/yhdt_earring.jpg'
            ];
            
            let loadedCount = 0;
            const totalImages = images.length;
            
            images.forEach(src => {
                const img = new Image();
                img.onload = () => {
                    loadedCount++;
                    if (loadedCount === totalImages) {
                        initTimeline();
                    }
                };
                img.onerror = () => {
                    console.warn(`图片加载失败: ${src}`);
                    loadedCount++;
                    if (loadedCount === totalImages) {
                        initTimeline();
                    }
                };
                img.src = src;
            });
        }

        // 淡出动画CSS
        const fadeOutStyle = document.createElement('style');
        fadeOutStyle.textContent = `
            @keyframes fadeOut {
                from { opacity: 1; }
                to { opacity: 0; }
            }
        `;
        document.head.appendChild(fadeOutStyle);

        // 页面加载完成后开始预加载
        document.addEventListener('DOMContentLoaded', () => {
            preloadImages();
        });

        // 备用启动方案（3秒后强制开始，防止图片加载卡住）
        setTimeout(() => {
            if (!elements.yhdt.style.animation) {
                console.log('备用启动：开始动画');
                initTimeline();
            }
        }, 3000);
    </script>
</body>
</html>