from manim import *
import os
from layout_manager import LayoutManager

'''
========== 动画素材清单 ==========

## 图片素材：
- 公职人员剪影图标: 公职人员剪影图标.png - 表示政府官员形象
- 制度漏洞图标: 制度漏洞图标.png - 表示制度存在的漏洞
- 财产申报文件图标: 财产申报文件图标.png - 表示财产申报制度
- 经商活动图标: 经商活动图标.png - 表示经商或商业活动
- 监督眼睛图标: 监督眼睛图标.png - 表示穿透式监督
- 财产公示文件图标: 财产公示文件图标.png - 表示财产公示制度
- 离职监管图标: 离职监管图标.png - 表示离职监管制度
- 全链条监督图标: 全链条监督图标.png - 表示监督的全过程链条
- 社会公平正义图标: 社会公平正义图标.png - 表示社会公平或正义
- 反腐倡廉图标: 反腐倡廉图标.png - 表示反腐倡廉

## 文字关键词素材：
- "制度反思" - 0s-6s - 无衬线粗体字，大号，深蓝色
- "暴露公职人员监管制度漏洞" - 0s-6s - 无衬线中等粗细字体，中号，黑色
- "财产申报" - 3s-6s - 无衬线粗体字，中号，红色
- "经商监管" - 3s-6s - 无衬线粗体字，中号，红色
- "加强制度建设与监管改革" - 6s-10s - 无衬线中等粗细字体，中号，黑色
- "穿透式监督" - 6s-10s - 无衬线粗体字，中号，蓝色
- "财产公示" - 6s-10s - 无衬线粗体字，中号，蓝色
- "离职监管" - 6s-10s - 无衬线粗体字，中号，蓝色
- "权力监督全链条审视" - 10s-14s - 无衬线中等粗细字体，中号，黑色
- "从个案追责到制度完善" - 10s-14s - 无衬线常规字体，中号，黑色
- "体现公众对社会公平的强烈诉求" - 14s-18s - 无衬线中等粗细字体，中号，黑色
- "和反腐倡廉的坚定意志" - 14s-18s - 无衬线加粗字体，中号，黑色，"坚定意志"四字渐变为深红色

## 动画总时长：18秒
=====================================
'''

class HuangYangDianTianEvent(Scene):
    def construct(self):
        # 设置背景色
        self.camera.background_color = WHITE
        
        # 检查图片资源是否存在
        image_paths = {
            "公职人员剪影图标": "公职人员剪影图标.png",
            "制度漏洞图标": "制度漏洞图标.png",
            "财产申报文件图标": "财产申报文件图标.png",
            "经商活动图标": "经商活动图标.png",
            "监督眼睛图标": "监督眼睛图标.png",
            "财产公示文件图标": "财产公示文件图标.png",
            "离职监管图标": "离职监管图标.png",
            "全链条监督图标": "全链条监督图标.png",
            "社会公平正义图标": "社会公平正义图标.png",
            "反腐倡廉图标": "反腐倡廉图标.png",
        }
        
        for name, path in image_paths.items():
            if not os.path.exists(path):
                print(f"警告: 图片资源 '{path}' 不存在!")
        
        # 第一阶段 (0s-3s): 制度反思与漏洞展示
        # 创建主标题
        title = Text("制度反思", font="SimHei", font_size=48, color=BLUE_E, weight=BOLD)
        title.to_edge(UP, buff=1)
        title.set_opacity(0)
        
        # 创建副标题
        subtitle = Text("暴露公职人员监管制度漏洞", font="SimHei", font_size=36, color=BLACK)
        subtitle.next_to(title, DOWN, buff=0.3)
        subtitle.set_opacity(0)
        
        # 创建制度漏洞图标
        leak_icon = ImageMobject(image_paths["制度漏洞图标"])
        leak_icon.height = 2
        leak_icon.width = leak_icon.height * 1.0  # 宽高比1.0
        leak_icon.move_to(ORIGIN)
        leak_icon.set_opacity(0)
        
        # 创建公职人员剪影图标
        official_icon = ImageMobject(image_paths["公职人员剪影图标"])
        official_icon.height = 2
        official_icon.width = official_icon.height * 1.0  # 宽高比1.0
        official_icon.move_to(LEFT * 2)
        official_icon.set_opacity(0)
        
        # 动画：标题从上方滑入
        self.play(
            FadeIn(title, shift=DOWN),
            run_time=1
        )
        
        # 动画：制度漏洞图标从中央放大出现
        self.play(
            FadeIn(leak_icon, scale=1.5),
            run_time=1
        )
        
        # 动画：公职人员剪影从左侧滑入
        self.play(
            FadeIn(official_icon, shift=RIGHT),
            FadeIn(subtitle, shift=UP),
            run_time=1
        )
        
        # 第二阶段 (3s-6s): 财产申报与经商监管
        # 创建财产申报文件图标
        property_report_icon = ImageMobject(image_paths["财产申报文件图标"])
        property_report_icon.height = 2
        property_report_icon.width = property_report_icon.height * 1.0  # 宽高比1.0
        property_report_icon.move_to(LEFT * 3)
        property_report_icon.set_opacity(0)
        
        # 创建经商活动图标
        business_icon = ImageMobject(image_paths["经商活动图标"])
        business_icon.height = 2
        business_icon.width = business_icon.height * 1.0  # 宽高比1.0
        business_icon.move_to(RIGHT * 3)
        business_icon.set_opacity(0)
        
        # 创建财产申报文字
        property_report_text = Text("财产申报", font="SimHei", font_size=36, color=RED, weight=BOLD)
        property_report_text.next_to(property_report_icon, DOWN, buff=0.5)
        property_report_text.set_opacity(0)
        
        # 创建经商监管文字
        business_text = Text("经商监管", font="SimHei", font_size=36, color=RED, weight=BOLD)
        business_text.next_to(business_icon, DOWN, buff=0.5)
        business_text.set_opacity(0)
        
        # 动画：公职人员剪影和漏洞图标上移
        self.play(
            official_icon.animate.shift(UP * 2),
            leak_icon.animate.shift(UP * 2),
            run_time=1
        )
        
        # 动画：财产申报和经商活动图标从两侧滑入
        self.play(
            FadeIn(property_report_icon, shift=RIGHT),
            FadeIn(business_icon, shift=LEFT),
            run_time=1
        )
        
        # 动画：文字关键词出现并强调
        self.play(
            FadeIn(property_report_text, scale=1.2),
            FadeIn(business_text, scale=1.2),
            run_time=1
        )
        
        # 创建虚线连接
        line1 = DashedLine(leak_icon.get_bottom(), property_report_icon.get_top(), color=GRAY)
        line2 = DashedLine(leak_icon.get_bottom(), business_icon.get_top(), color=GRAY)
        
        self.play(
            Create(line1),
            Create(line2),
            Flash(leak_icon, color=RED, flash_radius=0.3),
            run_time=1
        )
        
        # 第三阶段 (6s-10s): 加强制度建设与监管改革
        # 清除前一场景
        self.play(
            *[FadeOut(mob) for mob in [title, subtitle, official_icon, leak_icon, 
                                      property_report_icon, business_icon, 
                                      property_report_text, business_text, line1, line2]],
            run_time=1
        )
        
        # 创建新标题
        reform_title = Text("加强制度建设与监管改革", font="SimHei", font_size=36, color=BLACK)
        reform_title.to_edge(UP, buff=1)
        reform_title.set_opacity(0)
        
        # 创建监督眼睛图标
        supervision_icon = ImageMobject(image_paths["监督眼睛图标"])
        supervision_icon.height = 2
        supervision_icon.width = supervision_icon.height * 1.0  # 宽高比1.0
        supervision_icon.move_to(ORIGIN)
        supervision_icon.set_opacity(0)
        
        # 创建财产公示文件图标
        property_disclosure_icon = ImageMobject(image_paths["财产公示文件图标"])
        property_disclosure_icon.height = 2
        property_disclosure_icon.width = property_disclosure_icon.height * 1.0  # 宽高比1.0
        property_disclosure_icon.move_to(LEFT * 3 + DOWN)
        property_disclosure_icon.set_opacity(0)
        
        # 创建离职监管图标
        resignation_supervision_icon = ImageMobject(image_paths["离职监管图标"])
        resignation_supervision_icon.height = 2
        resignation_supervision_icon.width = resignation_supervision_icon.height * 1.0  # 宽高比1.0
        resignation_supervision_icon.move_to(RIGHT * 3 + DOWN)
        resignation_supervision_icon.set_opacity(0)
        
        # 创建穿透式监督文字
        supervision_text = Text("穿透式监督", font="SimHei", font_size=36, color=BLUE, weight=BOLD)
        supervision_text.next_to(supervision_icon, DOWN, buff=0.5)
        supervision_text.set_opacity(0)
        
        # 创建财产公示文字
        property_disclosure_text = Text("财产公示", font="SimHei", font_size=36, color=BLUE, weight=BOLD)
        property_disclosure_text.next_to(property_disclosure_icon, DOWN, buff=0.5)
        property_disclosure_text.set_opacity(0)
        
        # 创建离职监管文字
        resignation_supervision_text = Text("离职监管", font="SimHei", font_size=36, color=BLUE, weight=BOLD)
        resignation_supervision_text.next_to(resignation_supervision_icon, DOWN, buff=0.5)
        resignation_supervision_text.set_opacity(0)
        
        # 动画：新标题滑入
        self.play(
            FadeIn(reform_title, shift=DOWN),
            run_time=1
        )
        
        # 动画：监督眼睛图标从中央放大出现
        self.play(
            FadeIn(supervision_icon, scale=1.5),
            run_time=1
        )
        
        # 动画：财产公示和离职监管图标从两侧滑入
        self.play(
            FadeIn(property_disclosure_icon, shift=RIGHT),
            FadeIn(resignation_supervision_icon, shift=LEFT),
            run_time=1
        )
        
        # 动画：文字关键词出现
        self.play(
            FadeIn(supervision_text),
            FadeIn(property_disclosure_text),
            FadeIn(resignation_supervision_text),
            run_time=1
        )
        
        # 第四阶段 (10s-14s): 权力监督全链条审视
        # 创建全链条监督图标
        chain_supervision_icon = ImageMobject(image_paths["全链条监督图标"])
        chain_supervision_icon.height = 1.5
        chain_supervision_icon.width = chain_supervision_icon.height * 1.78  # 宽高比1.78
        chain_supervision_icon.move_to(ORIGIN)
        chain_supervision_icon.set_opacity(0)
        
        # 创建权力监督全链条审视标题
        chain_title = Text("权力监督全链条审视", font="SimHei", font_size=36, color=BLACK)
        chain_title.to_edge(UP, buff=1)
        chain_title.set_opacity(0)
        
        # 创建从个案追责到制度完善文字
        case_to_system_text = Text("从个案追责到制度完善", font="SimHei", font_size=36, color=BLACK)
        case_to_system_text.next_to(chain_supervision_icon, DOWN, buff=0.5)
        case_to_system_text.set_opacity(0)
        
        # 动画：缩小并上移前一场景的图标
        self.play(
            supervision_icon.animate.scale(0.75).shift(UP * 2),
            property_disclosure_icon.animate.scale(0.75).shift(UP * 2),
            resignation_supervision_icon.animate.scale(0.75).shift(UP * 2),
            FadeOut(supervision_text),
            FadeOut(property_disclosure_text),
            FadeOut(resignation_supervision_text),
            FadeOut(reform_title),
            run_time=1
        )
        
        # 动画：全链条监督图标从底部滑入
        self.play(
            FadeIn(chain_supervision_icon, shift=UP),
            FadeIn(chain_title, shift=DOWN),
            run_time=1
        )
        
        # 创建链条节点亮起的效果
        dots = [Dot(color=BLUE).move_to(chain_supervision_icon.get_center() + LEFT * (1.5 - i * 0.75)) for i in range(5)]
        for dot in dots:
            dot.set_opacity(0)
        
        # 动画：链条节点逐一亮起
        for dot in dots:
            self.play(
                FadeIn(dot, scale=1.2),
                run_time=0.4
            )
        
        # 动画：从个案追责到制度完善文字出现
        self.play(
            FadeIn(case_to_system_text),
            run_time=1
        )
        
        # 第五阶段 (14s-18s): 社会公平与反腐倡廉
        # 创建社会公平正义图标
        justice_icon = ImageMobject(image_paths["社会公平正义图标"])
        justice_icon.height = 2
        justice_icon.width = justice_icon.height * 1.0  # 宽高比1.0
        justice_icon.move_to(LEFT * 3)
        justice_icon.set_opacity(0)
        
        # 创建反腐倡廉图标
        anti_corruption_icon = ImageMobject(image_paths["反腐倡廉图标"])
        anti_corruption_icon.height = 2
        anti_corruption_icon.width = anti_corruption_icon.height * 1.0  # 宽高比1.0
        anti_corruption_icon.move_to(RIGHT * 3)
        anti_corruption_icon.set_opacity(0)
        
        # 创建体现公众对社会公平的强烈诉求文字
        justice_demand_text = Text("体现公众对社会公平的强烈诉求", font="SimHei", font_size=36, color=BLACK)
        justice_demand_text.move_to(UP)
        justice_demand_text.set_opacity(0)
        
        # 创建和反腐倡廉的坚定意志文字
        anti_corruption_will_text_parts = [
            Text("和反腐倡廉的", font="SimHei", font_size=36, color=BLACK),
            Text("坚定意志", font="SimHei", font_size=40, color=RED_E, weight=BOLD)
        ]
        
        anti_corruption_will_text_group = VGroup(*anti_corruption_will_text_parts)
        anti_corruption_will_text_group.arrange(RIGHT, buff=0.1)
        anti_corruption_will_text_group.move_to(DOWN)
        anti_corruption_will_text_group.set_opacity(0)
        
        # 动画：上移全链条监督图标
        self.play(
            chain_supervision_icon.animate.shift(UP * 2.5),
            FadeOut(supervision_icon),
            FadeOut(property_disclosure_icon),
            FadeOut(resignation_supervision_icon),
            FadeOut(chain_title),
            FadeOut(case_to_system_text),
            *[FadeOut(dot) for dot in dots],
            run_time=1
        )
        
        # 动画：社会公平正义和反腐倡廉图标从两侧滑入
        self.play(
            FadeIn(justice_icon, shift=RIGHT),
            FadeIn(anti_corruption_icon, shift=LEFT),
            run_time=1
        )
        
        # 动画：文字关键词从左右滑入
        self.play(
            FadeIn(justice_demand_text, shift=RIGHT),
            FadeIn(anti_corruption_will_text_group, shift=LEFT),
            run_time=1
        )
        
        # 动画：光芒效果从中心向外扩散
        glow = Dot(color=YELLOW, radius=0.1).move_to(ORIGIN)
        self.play(
            glow.animate.scale(20).set_opacity(0),
            run_time=1
        )
        
        # 保持最终画面2秒
        self.wait(2)
        
        # 淡出所有元素
        self.play(
            *[FadeOut(mob) for mob in self.mobjects],
            run_time=1
        )