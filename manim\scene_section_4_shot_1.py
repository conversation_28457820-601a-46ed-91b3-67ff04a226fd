from manim import *
import os
from layout_manager import LayoutManager

'''
========== 动画素材清单 ==========

## 图片素材：
- 黄杨钿甜的母亲: image.character - 司玲霞的正面照片
- 黄杨钿甜母亲的梵克雅宝项链: image.prop - 梵克雅宝项链的清晰展示图
- 黄杨钿甜母亲的卡地亚手镯: image.prop - 卡地亚手镯的清晰展示图
- 司玲霞移民咨询公司图示: 司玲霞移民咨询公司图示.png - 司玲霞经营的移民咨询公司的logo或公司照片

## 文字关键词素材：
- "黄杨钿甜母亲司玲霞" - 0.0s-3.0s - 微软雅黑加粗，24px，深灰色
- "备受关注的家庭成员" - 0.5s-3.0s - 等线体，18px，深灰色
- "2016年（黄杨钿甜父亲杨伟任职期间）" - 4.0s-7.0s - 等线体，16px，深灰色
- "梵克雅宝项链" - 4.5s-7.0s - 等线体，18px，深灰色
- "约50万元" - 5.0s-7.0s - Impact加粗，22px，红色
- "2018年" - 8.0s-11.0s - 等线体，16px，深灰色
- "卡地亚手镯" - 8.5s-11.0s - 等线体，18px，深灰色
- "约40多万元" - 9.0s-11.0s - Impact加粗，22px，红色
- "短期内高价值奢侈品获取时间线" - 11.0s-12.0s - 等线体，16px，深灰色
- "2016: 梵克雅宝项链 ≈50万元" - 11.2s-12.0s - 等线体，14px，深灰色
- "2018: 卡地亚手镯 ≈40万元" - 11.4s-12.0s - 等线体，14px，深灰色
- "涉足移民咨询业务" - 12.0s-15.0s - 等线体，18px，深灰色
- "非法签证和行贿问题" - 12.5s-15.0s - 等线体，18px，深黄色
- "来源：官方调查报道" - 12.7s-15.0s - 等线体，14px，深灰色
- "事发后选择'隐身'并删除炫富痕迹" - 13.0s-14.5s - 等线体，20px，深灰色

## 动画总时长：16秒
=====================================
'''

class HuangYangDianTianMotherInvestigation(Scene):
    def construct(self):
        # 设置背景色
        self.camera.background_color = "#FFFFFF"
        
        # 定义颜色
        DARK_GRAY = "#333333"
        DARK_YELLOW = "#CC9900"
        RED = "#FF0000"
        
        # 检查图片资源
        image_paths = {
            "mother": "image.character",
            "necklace": "image.prop",
            "bracelet": "image.prop",
            "company": "司玲霞移民咨询公司图示.png"
        }
        
        for name, path in image_paths.items():
            if not os.path.exists(path):
                print(f"警告: 图片文件 '{path}' 不存在，请确保提供正确的图片路径")
        
        # 场景1: 0.0s ~ 3.0s - 介绍黄杨钿甜母亲司玲霞
        # 创建母亲图片
        mother_img = ImageMobject(image_paths["mother"])
        # 根据宽高比0.75设置尺寸
        mother_img.height = 4.7
        mother_img.width = 3.5
        mother_img.move_to([-4, 0, 0])
        mother_img.set_opacity(0)
        
        # 创建文字元素
        title = Text("黄杨钿甜母亲司玲霞", font="SimHei", font_size=24, color=DARK_GRAY, weight=BOLD)
        title.move_to([3, 3, 0])
        title.set_opacity(0)
        
        subtitle = Text("备受关注的家庭成员", font="SimSun", font_size=18, color=DARK_GRAY)
        subtitle.move_to([3, 2.5, 0])
        subtitle.set_opacity(0)
        
        # 确保元素不重叠
        LayoutManager.ensure_screen_bounds(mother_img)
        LayoutManager.ensure_screen_bounds(title)
        LayoutManager.ensure_screen_bounds(subtitle)
        
        # 添加元素到场景并执行动画
        self.add(mother_img, title, subtitle)
        
        # 母亲照片从左侧滑入
        self.play(
            mother_img.animate.set_opacity(1),
            run_time=1.0
        )
        
        # 标题以打字机效果淡入
        self.play(
            AddTextLetterByLetter(title),
            run_time=1.0
        )
        
        # 副标题平滑显示
        self.play(
            FadeIn(subtitle),
            run_time=0.5
        )
        
        self.wait(0.5)  # 保持画面0.5秒
        
        # 场景2: 4.0s ~ 7.0s - 展示梵克雅宝项链
        # 创建母亲小图
        small_mother_img = mother_img.copy()
        small_mother_img.height = 2
        small_mother_img.width = 1.5
        small_mother_img.move_to([-5.5, 3, 0])
        
        # 创建项链图片
        necklace_img = ImageMobject(image_paths["necklace"])
        # 根据宽高比0.80设置尺寸
        necklace_img.height = 6.25
        necklace_img.width = 5
        necklace_img.move_to([2, 0, 0])
        necklace_img.set_opacity(0)
        
        # 创建文字元素
        year_2016 = Text("2016年（黄杨钿甜父亲杨伟任职期间）", font="SimSun", font_size=16, color=DARK_GRAY)
        year_2016.move_to([5, 3, 0])
        year_2016.set_opacity(0)
        
        necklace_label = Text("梵克雅宝项链", font="SimSun", font_size=18, color=DARK_GRAY)
        necklace_label.move_to([2, 1, 0])
        necklace_label.set_opacity(0)
        
        price_50w = Text("约50万元", font="Impact", font_size=22, color=RED, weight=BOLD)
        price_50w.move_to([2, -1, 0])
        price_50w.set_opacity(0)
        
        # 确保元素不重叠
        LayoutManager.ensure_screen_bounds(small_mother_img)
        LayoutManager.ensure_screen_bounds(necklace_img)
        LayoutManager.ensure_screen_bounds(year_2016)
        LayoutManager.ensure_screen_bounds(necklace_label)
        LayoutManager.ensure_screen_bounds(price_50w)
        
        # 添加元素到场景
        self.add(necklace_img, year_2016, necklace_label, price_50w)
        
        # 母亲照片缩小到左上角
        self.play(
            Transform(mother_img, small_mother_img),
            FadeOut(title),
            FadeOut(subtitle),
            run_time=1.0
        )
        
        # 项链图片显示
        self.play(
            necklace_img.animate.set_opacity(1),
            run_time=0.5
        )
        
        # 年份标签显示
        self.play(
            FadeIn(year_2016),
            run_time=0.5
        )
        
        # 项链标签显示
        self.play(
            FadeIn(necklace_label),
            run_time=0.5
        )
        
        # 价格标签以数字跳动效果显示
        self.play(
            CountInFrom(price_50w, 0),
            run_time=0.5
        )
        
        self.wait(1.0)  # 保持画面1秒
        
        # 场景3: 8.0s ~ 11.0s - 展示卡地亚手镯
        # 创建手镯图片
        bracelet_img = ImageMobject(image_paths["bracelet"])
        # 根据宽高比0.80设置尺寸
        bracelet_img.height = 6.25
        bracelet_img.width = 5
        bracelet_img.move_to([2, 0, 0])
        bracelet_img.set_opacity(0)
        
        # 创建文字元素
        year_2018 = Text("2018年", font="SimSun", font_size=16, color=DARK_GRAY)
        year_2018.move_to([5, 3, 0])
        year_2018.set_opacity(0)
        
        bracelet_label = Text("卡地亚手镯", font="SimSun", font_size=18, color=DARK_GRAY)
        bracelet_label.move_to([2, 1, 0])
        bracelet_label.set_opacity(0)
        
        price_40w = Text("约40多万元", font="Impact", font_size=22, color=RED, weight=BOLD)
        price_40w.move_to([2, -1, 0])
        price_40w.set_opacity(0)
        
        # 确保元素不重叠
        LayoutManager.ensure_screen_bounds(bracelet_img)
        LayoutManager.ensure_screen_bounds(year_2018)
        LayoutManager.ensure_screen_bounds(bracelet_label)
        LayoutManager.ensure_screen_bounds(price_40w)
        
        # 添加元素到场景
        self.add(bracelet_img, year_2018, bracelet_label, price_40w)
        
        # 交叉淡出项链，淡入手镯
        self.play(
            FadeOut(necklace_img),
            FadeIn(bracelet_img),
            run_time=0.5
        )
        
        # 年份标签更新
        self.play(
            FadeOut(year_2016),
            FadeIn(year_2018),
            run_time=0.5
        )
        
        # 手镯标签显示
        self.play(
            FadeOut(necklace_label),
            FadeIn(bracelet_label),
            run_time=0.5
        )
        
        # 价格标签以数字跳动效果显示
        self.play(
            FadeOut(price_50w),
            CountInFrom(price_40w, 0),
            run_time=0.5
        )
        
        self.wait(1.0)  # 保持画面1秒
        
        # 场景4: 11.0s ~ 12.0s - 展示时间线
        # 创建时间线标题
        timeline_title = Text("短期内高价值奢侈品获取时间线", font="SimSun", font_size=16, color=DARK_GRAY)
        timeline_title.move_to([0, 2, 0])
        timeline_title.set_opacity(0)
        
        # 创建时间线点位
        timeline_2016 = Text("2016: 梵克雅宝项链 ≈50万元", font="SimSun", font_size=14, color=DARK_GRAY)
        timeline_2016.move_to([-3, 0, 0])
        timeline_2016.set_opacity(0)
        
        timeline_2018 = Text("2018: 卡地亚手镯 ≈40万元", font="SimSun", font_size=14, color=DARK_GRAY)
        timeline_2018.move_to([3, 0, 0])
        timeline_2018.set_opacity(0)
        
        # 创建时间线
        timeline = Line([-5, 0, 0], [5, 0, 0], color=DARK_GRAY)
        timeline.set_opacity(0)
        
        # 创建时间点
        dot_2016 = Dot(point=[-3, 0, 0], color=RED)
        dot_2016.set_opacity(0)
        
        dot_2018 = Dot(point=[3, 0, 0], color=RED)
        dot_2018.set_opacity(0)
        
        # 确保元素不重叠
        LayoutManager.ensure_screen_bounds(timeline_title)
        LayoutManager.ensure_screen_bounds(timeline_2016)
        LayoutManager.ensure_screen_bounds(timeline_2018)
        
        # 添加元素到场景
        self.add(timeline, dot_2016, dot_2018, timeline_title, timeline_2016, timeline_2018)
        
        # 手镯和标签缩小移至右下角
        small_bracelet = bracelet_img.copy()
        small_bracelet.height = 1.5
        small_bracelet.width = 1.2
        small_bracelet.move_to([5.5, -3, 0])
        
        # 时间线元素显示
        self.play(
            FadeOut(bracelet_img),
            FadeOut(year_2018),
            FadeOut(bracelet_label),
            FadeOut(price_40w),
            FadeIn(timeline),
            run_time=0.3
        )
        
        self.play(
            FadeIn(timeline_title),
            run_time=0.2
        )
        
        self.play(
            FadeIn(dot_2016),
            FadeIn(timeline_2016),
            run_time=0.2
        )
        
        self.play(
            FadeIn(dot_2018),
            FadeIn(timeline_2018),
            run_time=0.3
        )
        
        # 场景5: 12.0s ~ 15.0s - 展示移民咨询业务和问题
        # 创建公司图示
        company_img = ImageMobject(image_paths["company"])
        # 根据宽高比1.33设置尺寸
        company_img.height = 3
        company_img.width = 4
        company_img.move_to([0, 0, 0])
        company_img.set_opacity(0)
        
        # 创建文字元素
        business_label = Text("涉足移民咨询业务", font="SimSun", font_size=18, color=DARK_GRAY)
        business_label.move_to([-4, 3, 0])
        business_label.set_opacity(0)
        
        # 创建带背景的警示框
        illegal_bg = Rectangle(height=0.8, width=4, color=DARK_YELLOW, fill_opacity=0.2)
        illegal_bg.move_to([4, 3, 0])
        illegal_bg.set_opacity(0)
        
        illegal_label = Text("非法签证和行贿问题", font="SimSun", font_size=18, color=DARK_YELLOW)
        illegal_label.move_to([4, 3, 0])
        illegal_label.set_opacity(0)
        
        source_label = Text("来源：官方调查报道", font="SimSun", font_size=14, color=DARK_GRAY)
        source_label.move_to([4, 2.5, 0])
        source_label.set_opacity(0)
        
        hidden_label = Text("事发后选择'隐身'并删除炫富痕迹", font="SimSun", font_size=20, color=DARK_GRAY)
        hidden_label.move_to([0, -3, 0])
        hidden_label.set_opacity(0)
        
        # 创建连接线
        line_to_business = Arrow(start=[-1, 0, 0], end=[-3, 2.5, 0], color=DARK_GRAY, buff=0.3)
        line_to_business.set_opacity(0)
        
        line_to_illegal = Arrow(start=[1, 0, 0], end=[3, 2.5, 0], color=DARK_GRAY, buff=0.3)
        line_to_illegal.set_opacity(0)
        
        # 确保元素不重叠
        LayoutManager.ensure_screen_bounds(company_img)
        LayoutManager.ensure_screen_bounds(business_label)
        LayoutManager.ensure_screen_bounds(illegal_bg)
        LayoutManager.ensure_screen_bounds(illegal_label)
        LayoutManager.ensure_screen_bounds(source_label)
        LayoutManager.ensure_screen_bounds(hidden_label)
        
        # 添加元素到场景
        self.add(company_img, business_label, illegal_bg, illegal_label, source_label, hidden_label, line_to_business, line_to_illegal)
        
        # 时间线向上移动
        self.play(
            timeline.animate.shift(UP * 2),
            dot_2016.animate.shift(UP * 2),
            dot_2018.animate.shift(UP * 2),
            timeline_title.animate.shift(UP * 2),
            timeline_2016.animate.shift(UP * 2),
            timeline_2018.animate.shift(UP * 2),
            run_time=0.5
        )
        
        # 公司图示显示
        self.play(
            FadeIn(company_img),
            run_time=0.5
        )
        
        # 业务标签和连接线显示
        self.play(
            FadeIn(business_label),
            FadeIn(line_to_business),
            run_time=0.5
        )
        
        # 非法问题标签、背景和连接线显示
        self.play(
            FadeIn(illegal_bg),
            FadeIn(illegal_label),
            FadeIn(line_to_illegal),
            run_time=0.5
        )
        
        # 来源标签显示
        self.play(
            FadeIn(source_label),
            run_time=0.2
        )
        
        # 隐身标签显示
        self.play(
            FadeIn(hidden_label),
            run_time=0.3
        )
        
        # 模拟删除效果
        self.play(
            ApplyWave(hidden_label),
            hidden_label.animate.set_opacity(0),
            run_time=0.5
        )
        
        # 所有元素淡出，模拟"隐身"效果
        self.play(
            *[FadeOut(mob) for mob in self.mobjects],
            run_time=1.0
        )
        
        # 最后一秒空白
        self.wait(1.0)