import os
import uuid
import hashlib
import json
from typing import Dict, Any, List, Tuple
from langchain_core.messages import BaseMessage

from utils.file_utils import FileManager
from utils.prompt_utils import PromptManager
from utils.s3_utils import SVGSaveS3
from utils.db_utils import write_project_video_scene, write_project_video_scene_material
from utils.svg_utils import SVGUtils
from llm.claude_local import ClaudeLocalProvider
from llm.batch_processor import BatchProcessor
from utils.db_pool import DBConnectionPool

class SVGGenerator:
    """SVG生成节点"""

    def __init__(self, conf_path="../conf/conf.ini"):
        """
        初始化SVG生成节点

        Args:
            conf_path: 配置文件路径
        """
        self.file_manager = FileManager()
        self.prompt_manager = PromptManager()
        self.llm = ClaudeLocalProvider()
        self.batch_processor = BatchProcessor(provider=self.llm, max_concurrent=10)
        self.conf_path = conf_path

        # 初始化S3存储工具
        self.s3_handler = SVGSaveS3(conf_path)

        # 初始化数据库连接池
        self.db_pool = DBConnectionPool(conf_path)

        # 创建SVG输出目录
        self.svg_output_dir = os.path.join("data", "svg_output")
        os.makedirs(self.svg_output_dir, exist_ok=True)

        # 生成唯一的项目ID
        self.project_id = None

    async def process(self, state: Dict[str, Any]) -> Dict[str, Any]:
        """
        处理数据

        Args:
            state: 当前状态

        Returns:
            Dict[str, Any]: 更新后的状态
        """
        print("\n" + "="*50)
        print("【开始执行】SVG生成 (SVGGenerator)")
        print("="*50)

        try:
            # 初始化项目信息和目录
            project_dir, project_hash = self._init_project_info(state)
            
            # 执行批处理生成SVG
            results = await self._generate_svg_batch(state, project_hash, project_dir)
            
            # 保存脚本文件
            data_state_path = self._save_script_file(state, project_dir, project_hash)
            
            # 更新状态和保存结果 - 注意这里不需要await，因为_update_state_with_results不是异步函数
            scene_paths = self._update_state_with_results(state, results)
            
            # 添加项目路径到状态中，供后续节点使用
            state["data"]["project_dir"] = project_dir
            state["data"]["data_state_path"] = data_state_path
            state["data"]["scene_paths"] = scene_paths
            
            state["current_step"] = "generate_svg"

            print("="*50)
            print("【完成执行】SVG生成 (SVGGenerator)")
            print("="*50 + "\n")

            return state
        finally:
            # 这里不需要显式关闭连接池，因为连接会自动返回到池中
            pass
            
    def _init_project_info(self, state: Dict[str, Any]) -> Tuple[str, str]:
        """
        初始化项目信息和目录
        
        Args:
            state: 当前状态
            
        Returns:
            Tuple[str, str]: 项目目录路径和项目哈希
        """
        # 获取热词和标题信息
        trend_word = state["data"].get("trend_word", "未知热词")
        title = state["data"].get("news_report", {}).get("title", "未知标题")
        print(f"热词: {trend_word}, 标题: {title}")

        # 生成唯一的项目标识符
        project_hash = hashlib.md5(f"{trend_word}".encode()).hexdigest()
        project_dir = f"svg_video/{project_hash}"
        print(f"项目目录: {project_dir}")

        # 创建项目目录
        os.makedirs(os.path.join(self.svg_output_dir, project_hash), exist_ok=True)
        
        return project_dir, project_hash
    
    async def _generate_svg_batch(self, state: Dict[str, Any], project_hash: str, project_dir: str) -> List[Tuple[str, str, str]]:
        """
        执行批处理生成SVG
        
        Args:
            state: 当前状态
            project_hash: 项目哈希
            project_dir: 项目目录
            
        Returns:
            List[Tuple[str, str, str]]: 生成结果列表，每项包含(场景ID, SVG代码, S3路径)
        """
        # 从news_report中获取SVG提示词
        news_report = state["data"].get("news_report", {})
        sections = news_report.get("sections", [])
        
        # 准备批处理数据
        batch_items = []

        # 收集所有需要处理的场景和镜头
        for i, section in enumerate(sections):
            section_id = f"section_{i+1}"
            
            # 获取当前场景的子镜头列表
            subsections = section.get("subsections", [])
            
            if subsections:
                # 如果有子镜头，则处理每个子镜头的svg_prompt
                for j, subsection in enumerate(subsections):
                    shot_id = f"{section_id}_shot_{j+1}"
                    svg_prompt = subsection.get("svg_prompt")
                    if svg_prompt:
                        batch_items.append((shot_id, svg_prompt))
                        print(f"准备处理场景 {section_id} 的镜头 {j+1} 的SVG生成")
            else:
                # 如果没有子镜头，则处理整个场景的svg_prompt
                svg_prompt = section.get("svg_prompt")
                if svg_prompt:
                    batch_items.append((section_id, svg_prompt))
                    print(f"准备处理场景 {i+1} 的SVG生成")

        print(f"准备批处理 {len(batch_items)} 个场景的SVG生成")
        
        # 定义一个异步的包装函数来处理响应
        async def process_svg_response_wrapper(item, response):
            return await self._process_svg_response(item, response, project_hash, project_dir)
        
        # 执行批处理（启用深度思考，使用temperature=0.0以获得确定性输出）
        results = await self.batch_processor.process_batch(
            items=batch_items,
            create_messages_func=self._create_messages_for_svg,
            process_response_func=process_svg_response_wrapper,
            max_retries=5,
            temperature=0.0
        )
        
        return results
    
    def _create_messages_for_svg(self, item: Tuple[str, str]) -> List[BaseMessage]:
        """
        创建SVG生成的消息
        
        Args:
            item: 包含场景ID和提示词的元组
            
        Returns:
            List[BaseMessage]: 消息列表
        """
        scene_id, svg_prompt = item
        print(f"准备处理场景: {scene_id}")
        return self.llm.create_messages(
            system_prompt="""
        ## **HTML电影级动画生成器**

### **核心使命**
基于提供的**《动画设计文档》**，你将作为一位顶级前端动画艺术家，创作一个符合1920×1080输出比例的**电影级HTML动画页面**。你的任务不仅仅是实现功能，更要将文档中的每一个细节转化为视觉叙事的一部分，追求**极致的流畅度、美学表现和情绪共鸣**。

### **设计理念与视觉风格**
*   **现代简约叙事**：以干净利落的视觉语言和电影般的镜头手法，高效且富有感染力地传达核心信息。
*   **沉浸式信息可视化**：将复杂数据和观点通过优雅的动效、精妙的排版和视觉层次，清晰、引人入胜地呈现。
*   **情绪动态调控**：根据叙事内容，通过色彩、光影、动效节奏和微交互，精准营造并引导观众的情绪体验。

### **技术实现核心规范**

#### **1. 基础框架与画布管理**
*   **容器尺寸**：`100vw × 100vh`，确保动画完全填充浏览器窗口，且内部内容始终保持**16:9**的固定宽高比。
*   **响应式布局**：所有元素的定位、尺寸**必须**使用视窗单位（`vw`），以确保动画在不同屏幕尺寸下均能完美适配16:9比例。
*   **兼容性**：支持Chrome、Firefox、Safari、Edge等主流浏览器，并针对录屏性能进行优化。
*   **结构**：生成单个HTML文件，包含内联CSS（`<style>`标签）和JavaScript（`<script>`标签）。

#### **2. 《动画设计文档》解析与坐标系转换**
这是最关键的部分。你必须**严格、精确地**解析文档中的以下信息，并将其转化为CSS可用的`vw`单位：

**2.1 全局单位与坐标系映射**
*   《动画设计文档》明确指定**画布尺寸为1920×1080像素 (16:9)**，并使用**14个宽度单位 × 8个高度单位**的网格进行视觉构成，其**中心点为(0,0)**。
*   为了在`100vw x 100vh`的HTML容器中精确复现16:9并遵循设计文档的单位，你需要定义一个**`base_unit_vw`**作为所有尺寸和位置的基准。
    *   **计算 `base_unit_vw` 和 `base_unit_vh`：**
        ```javascript
        // 假设屏幕宽度是100vw，并且要维持14个宽度单位 × 8个高度单位的输出比例
        // 14单位宽度对应100vw，所以1单位宽度 = 100/14 vw
        const base_unit_vw = 100 / 14; // 7.14vw
        // 这意味着设计文档中的1个"宽度单位"应等同于7.14vw
        
        // 8个高度单位对应100vh，所以1单位高度 = 100/8 vh
        const base_unit_vh = 100 / 8; // 12.5vh
        // 这意味着设计文档中的1个"高度单位"应等同于12.5vh
        ```
*   **元素尺寸转换**：
    *   设计文档中的元素 `尺寸 (宽x高)`，例如 `6x8 单位`：
        *   `CSS width = (设计文档宽度单位) * base_unit_vw`
        *   `CSS height = (设计文档高度单位) * base_unit_vh`
*   **元素位置转换**：
    *   设计文档中的 `位置 (X,Y 相对于视频中心点(0,0))`：
    *   HTML/CSS的定位通常以左上角为基准。你需要将设计文档的中心坐标系转换为左上角坐标系。
    *   `CSS left = calc(50vw + (设计文档X坐标 * base_unit_vw))`
    *   `CSS top = calc(50vh + (设计文档Y坐标 * base_unit_vh))`
        *   **重要提示：** 使用 `position: absolute; left: 50%; top: 50%; transform: translate(calc(-50% + X_offset_vw), calc(-50% + Y_offset_vh));` 其中：
            *   `X_offset_vw = (设计文档X坐标 * base_unit_vw)`
            *   `Y_offset_vh = (设计文档Y坐标 * base_unit_vh)`
        *   **优先推荐此方法，因为它更稳定且易于处理中心点定位。**

**2.2 元素素材处理**
*   **图片引用**：所有图片**必须**通过`<img>`标签引用。`src`路径严格对应`3.1`和`3.2`中的`资产路径`。
*   **图片加载**：动画开始前，确保所有图片已完全加载。使用JavaScript监听`load`事件，避免闪烁或错位。
*   **图片优化**：
    *   利用`object-fit: contain` 或 `object-fit: cover` 结合 `aspect-ratio` CSS属性，确保图片按文档中`宽高比`正确显示，且不失真。
    *   可根据场景需求，添加微妙的滤镜（`filter`，如`brightness`, `contrast`）、阴影（`box-shadow`）或边框效果，提升视觉质感。

**2.3 文字关键词元素处理**
*   **内容、字体、字号、颜色、位置**：**完全严格遵循**《动画设计文档》`3.4 文字关键词元素`和`4. 分镜/场景与音频同步列表`中的每一个指定细节。
*   **字号转换**：如果文档中字号是`px`单位，请转换为`vw`单位。例如，对于1920px宽的屏幕，`1px = 1/19.2 vw`。
*   **文本样式**：可添加`text-shadow`、`letter-spacing`等属性，增加文本的视觉表现力。

#### 3. 动画系统与时间轴精确性
**3.1 时间轴与元素同步：绝对优先级与毫厘不差**
*   **这是最核心的规则：** 你必须将《动画设计文档》中`3.1 视觉资产元素`、`3.2 背景元素`、`3.3 人物角色元素`和`3.4 文字关键词元素`中定义的**所有元素**，与`4. 分镜/场景与音频同步列表`中的**每一个时间戳范围**进行**逐一核对**。
*   **元素的出现与消失：**
    *   **严格按照**`4. 分镜/场景与音频同步列表`中`时间戳`（例如 `0s ~ 2s`）来**绝对精确地控制**元素的出现（`opacity: 1`或`display: block`）和消失。
    *   **确保元素准时入场，准时退场。** 不允许任何元素提前或延迟出现/消失。
    *   HTML视频画面中的元素内容，必须与`4. 分镜/场景与音频同步列表`中该时间戳范围内的元素**完全一致**。
    *   **不得凭空增加或删除任何元素。** 文档中未提及的元素不得出现，文档中提及的元素不得缺席。
*   **JavaScript时间轴控制：**
    *   使用JavaScript的`setTimeout`或`requestAnimationFrame`管理场景切换和元素动画的启动。
    *   每个分镜结束后，确保当前分镜的元素状态保持（`animation-fill-mode: forwards`）。
*   **Z-Index管理**：根据视觉层次和叙事需求，合理规划元素的`z-index`，确保前景、中景、背景元素之间的正确叠放关系。

### **视觉增强与叙事策略（智能创意填充）**

在**严格遵循设计文档中已明确指定**的动画与状态描述的基础上，对于**未明确提及或可进一步丰富**的细节，请你发挥创意，注入以下高级视觉效果，提升整体艺术性：

#### **1. 布局美学与空间感**
*   **动态黄金比例**：在元素入场、停留、退场的过程中，巧妙引导视觉焦点，使其在不同阶段符合经典的黄金比例构图。
*   **呼吸空间**：元素间保持充足的留白，利用背景的微动效或光影变化，营造画面的呼吸感。
*   **视觉引导线**：利用元素的排列、运动轨迹或光影，创建视觉引导线，指向关键信息。

#### **2. 元素入场与退场动画（创意补全）**
*   **入场动画**：为每个元素设计独特且优雅的入场方式。
    *   **图片/人物**：可以从模糊到清晰、从景深到前景（模拟电影推拉）、碎片化重组、3D翻转、光束汇聚、水墨散开等。
    *   **文字**：除了淡入、滑入，可尝试：
        *   **打字机效果**：如 `6s - 8s` 的引用文字，模拟逐字打印，可配合字符轻微弹跳。
        *   **秒表跳动/数字滚动**：如 `4s - 6s` 的价格数字，结合 `¥` 符号的动态变化，从数字跳动到最终确认，伴随独特的音效提示。
        *   **文字溶解/蒸汽效果**：用于强调或不稳定的文字。
        *   **霓虹灯/光晕效果**：用于标题或强调。
*   **退场动画**：设计流畅的退场方式，如渐隐、缩放消失、滑出、像素化消散、模糊并淡出。
*   **强调动效**：关键信息出现时，配合脉动、发光、颜色高亮、轻微抖动、光斑追踪等强调效果，吸引注意力。
*   **微交互感知**：即使是非交互动画，也可添加细腻的微动效，如背景元素的轻微浮动、呼吸效果，或环境光效变化。

#### **3. 色彩、光影与质感深化**
*   **色彩情绪流**：根据叙事的情绪曲线，动态调整整体色调和单个元素的色彩饱和度/亮度，增强氛围感。
*   **多层渐变应用**：巧妙运用线性、径向或锥形渐变，增加背景或元素的视觉深度和现代感。
*   **电影级光影**：通过动态阴影、高光、体积光、光束效果，模拟真实光照，增强立体感和戏剧张力。
*   **纹理细节**：在适当位置（如背景、文字）添加微妙的纹理或图案，提升画面的精致度和触感。

#### **4. 叙事增强策略（镜头语言与节奏大师）**
*   **模拟电影镜头**：
    *   **推拉摇移**：通过全局的`transform: scale()` 和 `translate()` 模拟镜头的推进、拉远、摇摄和移动，增强画面的纵深感和戏剧性。
    *   **景深效果**：利用`filter: blur()` 和 `transform: scale()` 模拟景深，引导观众焦点。
*   **蒙太奇手法**：通过画面元素的快速切换、重叠或并置，强化叙事节奏和对比效果。
*   **符号化表达**：运用抽象图形、几何体或光效，加强信息的视觉表达力（例如，问号的放大脉冲可结合破碎或扭曲的光线）。
*   **情绪曲线同步**：动画节奏（快慢、缓急）严格跟随故事的情绪起伏，在关键时刻加速，在思考处放缓，营造沉浸感。
*   **视觉高潮设计**：在关键信息点（如`¥230万`出现，`财富来源?`的问号出现）设计视觉高潮时刻，如强烈的光影、粒子爆发、震动效果、色彩冲击等。

#### **5. 音效提示（重要）**
*   虽然你不能直接生成音效，但请在JavaScript代码中，为设计文档中明确提及的音效点（如`闪光音效`、`计数音效`、`不和谐音效`、`打字效果音`、`悬疑音效`）添加**详细的注释**，指出在此处应该触发相应的音效，并提供建议的播放时机。

### **质量控制标准**

*   **像素完美与流畅度**：所有元素清晰锐利，动画保持**60fps**的视觉流畅度，无卡顿、掉帧。
*   **色彩准确性**：严格遵循设计文档的色彩规范。
*   **性能优化**：
    *   最小化DOM复杂度。
    *   优化CSS选择器，减少不必要的重绘重排。
    *   合理管理动画元素数量，避免内存泄漏。
*   **边界处理**：
    *   所有元素保持在安全显示区域内，**预留边缘缓冲**，防止裁切。
    *   防止任何元素意外溢出视窗边界。
    *   添加资源加载失败的降级方案（例如，图片无法加载时显示占位符或背景）。

### **输出要求**

*   生成**单个HTML文件**。
*   包含完整的HTML结构 (`<!DOCTYPE html>`, `<html>`, `<head>`, `<body>`)。
*   所有CSS样式**内联**在`<style>`标签中。
*   所有JavaScript动画控制逻辑**内联**在`<script>`标签中，置于`<body>`底部或使用`defer`属性。
*   **详细且清晰的代码注释**，解释关键的动画逻辑、时间戳控制、坐标转换和创意填充。
*   代码结构清晰，易于理解和后续修改。

---

**记住：** 你是电影的导演，代码是你的镜头。在遵循剧本的同时，用你的艺术天赋和技术实力，将这个动画提升到一个新的高度，让每一次观看都成为一次令人难忘的视觉之旅。

            """,
            history=[],
            query=svg_prompt
        )
    
    async def _process_svg_response(self, item: Tuple[str, str], response: str, project_hash: str, project_dir: str) -> Tuple[str, str, str]:
        """
        处理SVG生成响应
        
        Args:
            item: 包含场景ID和提示词的元组
            response: LLM生成的响应
            project_hash: 项目哈希
            project_dir: 项目目录
            
        Returns:
            Tuple[str, str, str]: 包含(场景ID, SVG代码, S3路径)的元组
        """
        scene_id, scene_prompt = item
        max_retries = 5
        retry_count = 0
        
        while retry_count < max_retries:
            try:
                # 提取SVG代码
                svg_code = SVGUtils.extract_svg_code(response)

                # 检查是否成功提取到SVG代码
                if not svg_code or '<svg' not in svg_code or '</svg>' not in svg_code:
                    print(f"第{retry_count + 1}次尝试：未能提取有效的SVG代码，将重新生成")
                    raise ValueError("SVG提取失败，需要重新生成")

                # 验证SVG代码的有效性
                is_valid, error_msg = SVGUtils.validate_svg(svg_code)
                if not is_valid:
                    print(f"第{retry_count + 1}次尝试：SVG代码验证失败: {error_msg}")
                    raise ValueError(f"SVG验证失败: {error_msg}")

                print(f"成功提取并验证SVG代码")

                # 保存SVG文件到本地并上传到S3
                s3_path = self._save_svg_file(scene_id, svg_code, project_hash, project_dir)
                
                return scene_id, svg_code, s3_path

            except Exception as e:
                retry_count += 1
                if retry_count >= max_retries:
                    print(f"达到最大重试次数({max_retries})，生成失败")
                    raise e
                print(f"第{retry_count}次尝试失败，准备第{retry_count + 1}次重试")
                # 重新生成SVG代码 - 使用异步方法
                messages = self.llm.create_messages(
                    system_prompt="你是一个专门从事SVG动画的AI助手。对用户提供的动画设计文档，生成相应的、完整的、有效的SVG代码。",
                    history=[],
                    query=scene_prompt
                )
                # 调用异步API生成响应，设置temperature为0
                result = await self.llm.generate_response(messages, temperature=0.0)
                response = result
    
    def _save_svg_file(self, scene_id: str, svg_code: str, project_hash: str, project_dir: str) -> str:
        """
        保存SVG文件到本地并上传到S3
        
        Args:
            scene_id: 场景ID
            svg_code: SVG代码
            project_hash: 项目哈希
            project_dir: 项目目录
            
        Returns:
            str: S3路径
        """
        # 保存SVG文件到本地
        svg_filename = f"scene_{scene_id}.svg"
        local_svg_path = os.path.join(self.svg_output_dir, project_hash, svg_filename)
        with open(local_svg_path, "w", encoding="utf-8") as f:
            f.write(svg_code)
        print(f"已保存场景 {scene_id} 的SVG文件到本地: {local_svg_path}")

        # 构建S3路径
        s3_svg_path = f"{project_dir}/{svg_filename}"

        # 上传SVG文件到S3
        try:
            self.s3_handler.put_object_by_content(s3_svg_path, svg_code)
            print(f"已上传场景 {scene_id} 的SVG文件到S3: {s3_svg_path}")
        except Exception as e:
            print(f"上传SVG文件到S3失败: {e}")
            
        return s3_svg_path
    
    def _save_script_file(self, state: Dict[str, Any], project_dir: str, project_hash: str) -> str:
        """
        保存脚本文件到本地和S3
        
        Args:
            state: 当前状态
            project_dir: 项目目录
            project_hash: 项目哈希
            
        Returns:
            str: 数据状态路径
        """
        # 保存脚本文件
        data_state_content = state["data"]
        data_state_path = f"{project_dir}/data_state.json"
        local_data_state_path = os.path.join(self.svg_output_dir, project_hash, "data_state.json")

        # 保存脚本到本地
        # 创建目录路径（如果不存在）
        os.makedirs(os.path.dirname(local_data_state_path), exist_ok=True)
        
        # 直接使用open写入，避免使用file_manager.write_json
        with open(local_data_state_path, "w", encoding="utf-8") as f:
            json.dump(data_state_content, f, ensure_ascii=False, indent=2)

        # 上传脚本到S3
        try:
            self.s3_handler.put_object_by_content(data_state_path, json.dumps(data_state_content, ensure_ascii=False, indent=2), mimetype="application/json")
            print(f"已上传脚本文件到S3: {data_state_path}")
        except Exception as e:
            print(f"上传脚本文件到S3失败: {e}")
            
        return data_state_path
    
    def _update_state_with_results(self, state: Dict[str, Any], results: List[Tuple[str, str, str]]) -> List[Tuple[str, str]]:
        """
        更新状态中的SVG文件信息
        
        Args:
            state: 当前状态
            results: 生成结果列表
            
        Returns:
            List[Tuple[str, str]]: 场景路径列表，每项包含(场景ID, S3路径)
        """
        # 更新状态
        state["data"]["svg_files"] = state["data"].get("svg_files", {})
        state["data"]["svg_paths"] = state["data"].get("svg_paths", {})
        
        # 将结果添加到状态中
        scene_paths = []
        for scene_id, svg_code, s3_path in results:
            state["data"]["svg_files"][scene_id] = svg_code
            state["data"]["svg_paths"][scene_id] = s3_path
            scene_paths.append((scene_id, s3_path))
            
            # 将SVG代码和路径添加到news_report的对应分镜中
            self._update_news_report_with_svg(state, scene_id, svg_code, s3_path)
            
        return scene_paths
    
    def _update_news_report_with_svg(self, state: Dict[str, Any], scene_id: str, svg_code: str, s3_path: str) -> None:
        """
        将SVG代码和路径添加到news_report的对应分镜中
        
        Args:
            state: 当前状态
            scene_id: 场景ID
            svg_code: SVG代码
            s3_path: S3路径
        """
        # 获取news_report
        news_report = state["data"].get("news_report", {})
        sections = news_report.get("sections", [])
        
        # 解析场景ID
        if "_shot_" in scene_id:
            # 镜头格式: section_X_shot_Y
            parts = scene_id.split('_')
            section_index = int(parts[1]) - 1
            shot_index = int(parts[3]) - 1
            
            # 确保索引有效
            if 0 <= section_index < len(sections) and "subsections" in sections[section_index]:
                subsections = sections[section_index].get("subsections", [])
                if 0 <= shot_index < len(subsections):
                    # 更新子镜头的svg_code和svg_path
                    sections[section_index]["subsections"][shot_index]["svg_code"] = svg_code
                    sections[section_index]["subsections"][shot_index]["svg_path"] = s3_path
                    print(f"已更新 {scene_id} 的svg_code和svg_path")
        else:
            # 普通场景格式: section_X
            section_index = int(scene_id.split('_')[1]) - 1
            if 0 <= section_index < len(sections):
                # 更新场景的svg_code和svg_path
                sections[section_index]["svg_code"] = svg_code
                sections[section_index]["svg_path"] = s3_path
                print(f"已更新 {scene_id} 的svg_code和svg_path")
        
        # 更新news_report
        state["data"]["news_report"] = news_report