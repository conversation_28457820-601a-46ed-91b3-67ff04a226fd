#!/usr/bin/env python3
"""
测试 ManimGenerator 节点的完整功能
"""

import asyncio
import json
import os
import tempfile
from unittest.mock import Mock, patch, AsyncMock
from nodes.html_generator import ManimGenerator

def create_test_state():
    """创建测试用的状态数据"""
    return {
        "data": {
            "trend_word": "测试热词",
            "news_report": {
                "title": "测试新闻标题",
                "sections": [
                    {
                        "svg_prompt": """
                        # 动画设计文档
                        
                        ## 场景描述
                        创建一个简单的圆形动画，圆形从左侧移动到右侧。
                        
                        ## 元素列表
                        - 圆形：红色，半径为1
                        - 背景：白色
                        
                        ## 动画时间线
                        - 0-2秒：圆形从左侧(-3,0)移动到右侧(3,0)
                        """
                    },
                    {
                        "subsections": [
                            {
                                "svg_prompt": """
                                # 动画设计文档 - 子场景1
                                
                                ## 场景描述
                                创建一个文字动画，显示"Hello Manim"。
                                
                                ## 元素列表
                                - 文字："Hello Manim"，蓝色，大小48
                                
                                ## 动画时间线
                                - 0-1秒：文字淡入
                                - 1-2秒：文字放大1.5倍
                                """
                            },
                            {
                                "svg_prompt": """
                                # 动画设计文档 - 子场景2
                                
                                ## 场景描述
                                创建一个正方形旋转动画。
                                
                                ## 元素列表
                                - 正方形：绿色，边长为2
                                
                                ## 动画时间线
                                - 0-3秒：正方形旋转360度
                                """
                            }
                        ]
                    }
                ]
            }
        },
        "current_step": "test"
    }

class MockS3Handler:
    """模拟S3处理器"""
    def __init__(self, conf_path):
        self.conf_path = conf_path
        self.uploaded_files = {}
    
    def put_object_by_content(self, s3_path, content, mimetype=None):
        """模拟上传文件到S3"""
        self.uploaded_files[s3_path] = content
        print(f"模拟上传到S3: {s3_path}")

class MockDBPool:
    """模拟数据库连接池"""
    def __init__(self, conf_path):
        self.conf_path = conf_path

class MockLLMProvider:
    """模拟LLM提供者"""
    def create_messages(self, system_prompt, history, query):
        return [{"role": "user", "content": query}]
    
    async def generate_response(self, messages, temperature=1.0):
        """模拟生成Manim代码响应"""
        return """
        这是生成的Manim代码：
        
        ```python
        from manim import *
        
        class TestScene(Scene):
            def construct(self):
                # 设置画布背景为白色
                self.camera.background_color = WHITE
                
                # 创建圆形
                circle = Circle(radius=1, color=RED)
                circle.move_to(LEFT * 3)
                
                # 添加到场景
                self.add(circle)
                
                # 动画：圆形移动
                self.play(
                    circle.animate.move_to(RIGHT * 3),
                    run_time=2
                )
        ```
        """

class MockBatchProcessor:
    """模拟批处理器"""
    def __init__(self, provider, max_concurrent):
        self.provider = provider
        self.max_concurrent = max_concurrent
    
    async def process_batch(self, items, create_messages_func, process_response_func, max_retries=5, temperature=0.0):
        """模拟批处理"""
        results = []
        for item in items:
            # 创建消息
            messages = create_messages_func(item)
            # 生成响应
            response = await self.provider.generate_response(messages, temperature)
            # 处理响应
            result = await process_response_func(item, response)
            results.append(result)
        return results

async def test_manim_generator():
    """测试ManimGenerator的主要功能"""
    print("开始测试 ManimGenerator...")
    
    # 创建临时目录用于测试
    with tempfile.TemporaryDirectory() as temp_dir:
        # 修补相关的类和方法
        with patch('nodes.manim_generator.SVGSaveS3', MockS3Handler), \
             patch('nodes.manim_generator.DBConnectionPool', MockDBPool), \
             patch('nodes.manim_generator.ClaudeLocalProvider', MockLLMProvider), \
             patch('nodes.manim_generator.BatchProcessor', MockBatchProcessor), \
             patch('utils.file_utils.FileManager'), \
             patch('utils.prompt_utils.PromptManager'):
            
            # 创建ManimGenerator实例
            generator = ManimGenerator()
            
            # 修改输出目录到临时目录
            generator.manim_output_dir = temp_dir
            
            # 创建测试状态
            test_state = create_test_state()
            
            print("测试状态数据:")
            print(json.dumps(test_state, ensure_ascii=False, indent=2))
            print("-" * 60)
            
            # 执行处理
            result_state = await generator.process(test_state)
            
            print("处理结果:")
            print(f"当前步骤: {result_state['current_step']}")
            print(f"项目目录: {result_state['data'].get('project_dir', 'N/A')}")
            print(f"数据状态路径: {result_state['data'].get('data_state_path', 'N/A')}")
            print(f"场景路径数量: {len(result_state['data'].get('scene_paths', []))}")
            print("-" * 60)
            
            # 检查生成的文件
            print("生成的Manim文件:")
            manim_files = result_state['data'].get('manim_files', {})
            for scene_id, code in manim_files.items():
                print(f"\n场景ID: {scene_id}")
                print(f"代码长度: {len(code)} 字符")
                print("代码预览:")
                print(code[:200] + "..." if len(code) > 200 else code)
                print("-" * 40)
            
            # 检查S3路径
            print("S3路径:")
            manim_paths = result_state['data'].get('manim_paths', {})
            for scene_id, path in manim_paths.items():
                print(f"场景ID: {scene_id} -> S3路径: {path}")
            
            # 检查本地文件
            print(f"\n本地文件目录: {temp_dir}")
            for root, dirs, files in os.walk(temp_dir):
                for file in files:
                    file_path = os.path.join(root, file)
                    print(f"本地文件: {file_path}")
                    if file.endswith('.py'):
                        with open(file_path, 'r', encoding='utf-8') as f:
                            content = f.read()
                            print(f"文件内容长度: {len(content)} 字符")
            
            # 检查news_report更新
            print("\nnews_report更新检查:")
            sections = result_state['data']['news_report']['sections']
            for i, section in enumerate(sections):
                if 'manim_code' in section:
                    print(f"Section {i+1}: 包含manim_code")
                if 'manim_path' in section:
                    print(f"Section {i+1}: 包含manim_path")
                if 'subsections' in section:
                    for j, subsection in enumerate(section['subsections']):
                        if 'manim_code' in subsection:
                            print(f"Section {i+1}, Subsection {j+1}: 包含manim_code")
                        if 'manim_path' in subsection:
                            print(f"Section {i+1}, Subsection {j+1}: 包含manim_path")

def test_manim_utils():
    """测试ManimUtils工具类"""
    print("\n" + "="*60)
    print("测试 ManimUtils...")
    
    from utils.manim_utils import ManimUtils
    
    # 测试代码提取
    test_response = """
    这是AI的回复，包含Manim代码：
    
    ```python
    from manim import *
    
    class MyScene(Scene):
        def construct(self):
            circle = Circle()
            self.play(Create(circle))
    ```
    
    这是结尾说明。
    """
    
    extracted_code = ManimUtils.extract_manim_code(test_response)
    print("提取的代码:")
    print(extracted_code)
    
    # 测试类名提取
    class_name = ManimUtils.extract_class_name(extracted_code)
    print(f"\n提取的类名: {class_name}")

async def main():
    """主测试函数"""
    print("="*60)
    print("ManimGenerator 完整测试")
    print("="*60)
    
    # 测试工具类
    test_manim_utils()
    
    # 测试主要功能
    await test_manim_generator()
    
    print("\n" + "="*60)
    print("测试完成！")
    print("="*60)

if __name__ == "__main__":
    asyncio.run(main())
