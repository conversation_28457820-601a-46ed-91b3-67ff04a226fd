from manim import *
import os
from layout_manager import LayoutManager

'''
========== 动画素材清单 ==========

## 图片素材：
- 黄杨钿甜形象: 黄杨钿甜形象.jpg - 17岁艺人形象展示
- 格拉夫耳环: 格拉夫耳环.png - 事件核心物品，疑似价值230万元的耳环
- 公务员象征: 公务员象征.png - 代表黄杨钿甜父亲的公务员身份
- 收入对比图表: 收入对比图表.png - 展示公务员收入与耳环价格的对比
- 正义天平: 正义天平.png - 象征社会公平与监督
- 价格标签: 价格标签.png - 强调230万元高价值
- 监督象征: 监督象征.png - 象征监督的图标

## 文字关键词素材：
- "核心简述" - 0.0s-7.0s - 黑体加粗28px
- "疑似价值 230万元" - 3.0s-7.0s - 微软雅黑加粗24px红色
- "格拉夫耳环" - 3.0s-7.0s - 黑体20px
- "17岁艺人 黄杨钿甜" - 7.0s-11.0s - 黑体加粗22px
- "风口浪尖" - 7.0s-11.0s - 微软雅黑加粗22px深蓝色
- "父亲：杨伟" - 11.0s-15.0s - 黑体20px
- "前雅安市公务员" - 11.0s-15.0s - 黑体加粗20px
- "巨额财富 vs 公职收入" - 15.0s-19.0s - 微软雅黑加粗24px
- "反差" - 15.0s-19.0s - 微软雅黑加粗24px红色
- "奢侈品真伪？" - 19.0s-23.0s - 黑体22px，问号为红色
- "公职廉洁性？" - 19.0s-23.0s - 黑体22px，问号为红色
- "官方调查已介入" - 19.0s-23.0s - 黑体加粗22px暗红色
- "公权力监督" - 23.0s-32.0s - 微软雅黑加粗24px
- "与" - 23.0s-32.0s - 微软雅黑18px
- "社会公平" - 23.0s-32.0s - 微软雅黑加粗24px
- "深度拷问" - 27.0s-32.0s - 微软雅黑加粗24px深蓝色
- "扫描二维码关注最新进展" - 29.0s-32.0s - 黑体18px

## 动画总时长：32秒
=====================================
'''

class EarringControversy(Scene):
    def construct(self):
        # 设置背景色
        self.camera.background_color = "#FFFFFF"
        
        # 检查文件是否存在的函数
        def check_file_exists(file_path):
            if not os.path.exists(file_path):
                print(f"警告: 文件 {file_path} 不存在!")
                return False
            return True
        
        # 资产路径
        earring_path = "格拉夫耳环.png"
        artist_path = "黄杨钿甜形象.jpg"
        civil_servant_path = "公务员象征.png"
        income_chart_path = "收入对比图表.png"
        justice_scale_path = "正义天平.png"
        price_tag_path = "价格标签.png"
        supervision_path = "监督象征.png"
        
        # 检查所有资产文件
        for path in [earring_path, artist_path, civil_servant_path, income_chart_path, 
                    justice_scale_path, price_tag_path, supervision_path]:
            check_file_exists(path)
        
        # 时间戳：0.0s ~ 3.0s
        # 创建格拉夫耳环图片
        earring = ImageMobject(earring_path) if check_file_exists(earring_path) else Square(color=RED)
        earring.height = 2.25
        earring.width = 4  # 宽高比1.78
        earring.set_opacity(0)
        
        # 创建"核心简述"文字
        title = Text("核心简述", font="SimHei", font_size=28, weight=BOLD, color=BLACK)
        title.to_edge(UP, buff=1)
        title.set_opacity(0)
        
        # 动画：耳环从小到大，标题滑入
        self.play(
            FadeIn(earring, scale=0.5),
            title.animate.set_opacity(1),
            run_time=1
        )
        
        self.play(
            earring.animate.scale(1.2),
            run_time=2
        )
        
        # 时间戳：3.0s ~ 7.0s
        # 创建价格标签
        price_tag = ImageMobject(price_tag_path) if check_file_exists(price_tag_path) else Square(color=GREEN)
        price_tag.height = 1.5
        price_tag.width = 2  # 宽高比1.33
        price_tag.set_opacity(0)
        
        # 安全定位价格标签
        price_tag_pos = np.array([5, 2, 0])
        price_tag.move_to(price_tag_pos)
        LayoutManager.ensure_screen_bounds(price_tag)
        
        # 创建文字元素
        price_text = Text("疑似价值 230万元", font="Microsoft YaHei", font_size=24, weight=BOLD, color=RED)
        price_text.move_to(np.array([5, 3, 0]))
        price_text.set_opacity(0)
        
        earring_text = Text("格拉夫耳环", font="SimHei", font_size=20, color=BLACK)
        earring_text.move_to(np.array([-5, -3, 0]))
        earring_text.set_opacity(0)
        
        # 动画：价格标签滑入，价格文字打字机效果
        self.play(
            earring.animate.scale(0.9),
            FadeIn(price_tag, shift=LEFT),
            run_time=1
        )
        
        # 打字机效果
        for i in range(len("疑似价值 230万元") + 1):
            self.play(
                Transform(price_text, 
                         Text("疑似价值 230万元"[:i], font="Microsoft YaHei", font_size=24, weight=BOLD, color=RED).move_to(price_text.get_center())),
                run_time=0.1
            )
        
        # 闪烁一次强调
        self.play(
            price_text.animate.scale(1.2),
            run_time=0.4
        )
        self.play(
            price_text.animate.scale(1/1.2),
            run_time=0.4
        )
        
        # 显示耳环文字
        self.play(
            FadeIn(earring_text),
            run_time=0.6
        )
        
        self.wait(0.5)  # 调整总时长到7秒
        
        # 时间戳：7.0s ~ 11.0s
        # 创建黄杨钿甜形象
        artist = ImageMobject(artist_path) if check_file_exists(artist_path) else Square(color=BLUE)
        artist.height = 3
        artist.width = 4  # 宽高比1.33
        artist.set_opacity(0)
        artist.move_to(np.array([-3, 0, 0]))
        
        # 创建文字元素
        artist_text = Text("17岁艺人 黄杨钿甜", font="SimHei", font_size=22, weight=BOLD, color=BLACK)
        artist_text.next_to(artist, DOWN, buff=0.5)
        artist_text.set_opacity(0)
        
        storm_text = Text("风口浪尖", font="Microsoft YaHei", font_size=22, weight=BOLD, color=BLUE_E)
        storm_text.move_to(np.array([5, 3, 0]))
        storm_text.set_opacity(0)
        
        # 动画：淡出前一场景元素，淡入新元素
        self.play(
            FadeOut(earring),
            FadeOut(price_tag),
            FadeOut(price_text),
            FadeOut(earring_text),
            FadeIn(artist, shift=RIGHT),
            run_time=1
        )
        
        self.play(
            FadeIn(artist_text),
            run_time=1
        )
        
        # 波浪状动效
        def wave_path(t):
            return np.array([5 + 0.2*np.sin(5*t), 3 + 0.1*np.cos(3*t), 0])
        
        storm_text.move_to(wave_path(0))
        self.play(
            FadeIn(storm_text),
            run_time=0.5
        )
        
        storm_path = ParametricFunction(wave_path, t_range=[0, 2*PI], color=WHITE)
        self.play(
            MoveAlongPath(storm_text, storm_path),
            run_time=1.5
        )
        
        self.wait(1)  # 调整总时长到11秒
        
        # 时间戳：11.0s ~ 15.0s
        # 创建公务员象征
        civil_servant = ImageMobject(civil_servant_path) if check_file_exists(civil_servant_path) else Square(color=YELLOW)
        civil_servant.height = 3
        civil_servant.width = 4  # 宽高比1.33
        civil_servant.set_opacity(0)
        civil_servant.move_to(np.array([3, 0, 0]))
        
        # 创建文字元素
        father_text = Text("父亲：杨伟", font="SimHei", font_size=20, color=BLACK)
        father_text.move_to(np.array([3, 1, 0]))
        father_text.set_opacity(0)
        
        job_text = Text("前雅安市公务员", font="SimHei", font_size=20, weight=BOLD, color=BLACK)
        job_text.move_to(np.array([3, 0, 0]))
        job_text.set_opacity(0)
        
        # 创建虚线连接
        dashed_line = DashedLine(artist.get_right(), civil_servant.get_left(), color=GRAY)
        dashed_line.set_opacity(0)
        
        # 动画：艺人图片缩小移至左侧，公务员象征滑入
        self.play(
            artist.animate.scale(0.8).move_to(np.array([-5, 0, 0])),
            FadeOut(storm_text),
            run_time=1
        )
        
        self.play(
            FadeIn(civil_servant, shift=LEFT),
            run_time=1
        )
        
        # 显示父亲文字和职业
        self.play(
            FadeIn(father_text),
            run_time=0.5
        )
        
        self.play(
            FadeIn(job_text),
            run_time=0.5
        )
        
        # 显示虚线连接
        self.play(
            FadeIn(dashed_line),
            run_time=1
        )
        
        self.wait(1)  # 调整总时长到15秒
        
        # 时间戳：15.0s ~ 19.0s
        # 创建收入对比图表
        income_chart = ImageMobject(income_chart_path) if check_file_exists(income_chart_path) else Rectangle(width=10, height=5, color=GREEN)
        income_chart.height = 5
        income_chart.width = 10  # 宽高比1.78
        income_chart.set_opacity(0)
        
        # 创建文字元素
        chart_title = Text("巨额财富 vs 公职收入", font="Microsoft YaHei", font_size=24, weight=BOLD, color=BLACK)
        chart_title.to_edge(UP, buff=1)
        chart_title.set_opacity(0)
        
        contrast_text = Text("反差", font="Microsoft YaHei", font_size=24, weight=BOLD, color=RED)
        contrast_text.move_to(ORIGIN)
        contrast_text.set_opacity(0)
        
        # 动画：淡出前一场景元素，图表从底部滑入
        self.play(
            FadeOut(artist),
            FadeOut(artist_text),
            FadeOut(civil_servant),
            FadeOut(father_text),
            FadeOut(job_text),
            FadeOut(dashed_line),
            run_time=1
        )
        
        self.play(
            FadeIn(income_chart, shift=UP),
            FadeIn(chart_title),
            run_time=1
        )
        
        # 反差文字强调效果
        self.play(
            FadeIn(contrast_text),
            run_time=0.5
        )
        
        self.play(
            contrast_text.animate.scale(1.3),
            run_time=0.5
        )
        
        self.play(
            contrast_text.animate.scale(1/1.3),
            run_time=0.5
        )
        
        self.wait(0.5)  # 调整总时长到19秒
        
        # 时间戳：19.0s ~ 23.0s
        # 创建监督象征
        supervision = ImageMobject(supervision_path) if check_file_exists(supervision_path) else Circle(color=PURPLE)
        supervision.height = 2.25
        supervision.width = 3  # 宽高比1.33
        supervision.set_opacity(0)
        supervision.move_to(np.array([5, -2, 0]))
        
        # 创建文字元素
        luxury_text = Text("奢侈品真伪？", font="SimHei", font_size=22, color=BLACK)
        luxury_text.move_to(np.array([-3, 0, 0]))
        luxury_text.set_opacity(0)
        
        # 设置问号为红色
        luxury_question = Text("？", font="SimHei", font_size=22, color=RED)
        luxury_question.next_to(luxury_text, RIGHT, buff=-0.1)
        luxury_question.set_opacity(0)
        
        integrity_text = Text("公职廉洁性？", font="SimHei", font_size=22, color=BLACK)
        integrity_text.move_to(np.array([3, 0, 0]))
        integrity_text.set_opacity(0)
        
        # 设置问号为红色
        integrity_question = Text("？", font="SimHei", font_size=22, color=RED)
        integrity_question.next_to(integrity_text, RIGHT, buff=-0.1)
        integrity_question.set_opacity(0)
        
        investigation_text = Text("官方调查已介入", font="SimHei", font_size=22, weight=BOLD, color="#8B0000")
        investigation_text.move_to(np.array([0, -3, 0]))
        investigation_text.set_opacity(0)
        
        # 动画：收入对比图表缩小移至上方，监督象征滑入
        self.play(
            income_chart.animate.scale(0.8).move_to(np.array([0, 2, 0])),
            FadeOut(contrast_text),
            run_time=1
        )
        
        self.play(
            FadeIn(supervision, shift=LEFT),
            run_time=1
        )
        
        # 显示两个焦点问题文字
        self.play(
            FadeIn(luxury_text, shift=RIGHT),
            FadeIn(integrity_text, shift=LEFT),
            run_time=1
        )
        
        # 问号闪烁效果
        self.play(
            FadeIn(luxury_question),
            FadeIn(integrity_question),
            run_time=0.5
        )
        
        self.play(
            luxury_question.animate.scale(1.2),
            integrity_question.animate.scale(1.2),
            run_time=0.3
        )
        
        self.play(
            luxury_question.animate.scale(1/1.2),
            integrity_question.animate.scale(1/1.2),
            run_time=0.2
        )
        
        # 官方调查文字从底部上升
        self.play(
            FadeIn(investigation_text, shift=UP),
            run_time=1
        )
        
        self.wait(0)  # 调整总时长到23秒
        
        # 时间戳：23.0s ~ 27.0s
        # 创建正义天平
        justice_scale = ImageMobject(justice_scale_path) if check_file_exists(justice_scale_path) else Rectangle(width=6, height=3.37, color=ORANGE)
        justice_scale.height = 3.37
        justice_scale.width = 6  # 宽高比1.78
        justice_scale.set_opacity(0)
        
        # 创建文字元素
        supervision_text = Text("公权力监督", font="Microsoft YaHei", font_size=24, weight=BOLD, color=BLACK)
        supervision_text.move_to(np.array([-3, 0, 0]))
        supervision_text.set_opacity(0)
        
        and_text = Text("与", font="Microsoft YaHei", font_size=18, color=BLACK)
        and_text.move_to(ORIGIN)
        and_text.set_opacity(0)
        
        fairness_text = Text("社会公平", font="Microsoft YaHei", font_size=24, weight=BOLD, color=BLACK)
        fairness_text.move_to(np.array([3, 0, 0]))
        fairness_text.set_opacity(0)
        
        # 动画：淡出前一场景元素，天平从底部滑入
        self.play(
            FadeOut(income_chart),
            FadeOut(chart_title),
            FadeOut(supervision),
            FadeOut(luxury_text),
            FadeOut(luxury_question),
            FadeOut(integrity_text),
            FadeOut(integrity_question),
            FadeOut(investigation_text),
            run_time=1
        )
        
        self.play(
            FadeIn(justice_scale, shift=UP),
            run_time=1
        )
        
        # 天平轻微摆动效果
        self.play(
            justice_scale.animate.rotate(0.05),
            run_time=0.5
        )
        
        self.play(
            justice_scale.animate.rotate(-0.1),
            run_time=0.5
        )
        
        self.play(
            justice_scale.animate.rotate(0.05),
            run_time=0.5
        )
        
        # 显示文字
        self.play(
            FadeIn(supervision_text),
            run_time=0.5
        )
        
        self.play(
            FadeIn(fairness_text),
            run_time=0.5
        )
        
        self.play(
            FadeIn(and_text),
            run_time=0.5
        )
        
        self.wait(1)  # 调整总时长到27秒
        
        # 时间戳：27.0s ~ 32.0s
        # 创建监督象征（缩小版）
        small_supervision = ImageMobject(supervision_path) if check_file_exists(supervision_path) else Circle(color=PURPLE)
        small_supervision.height = 1.5
        small_supervision.width = 2  # 宽高比1.33
        small_supervision.set_opacity(0)
        small_supervision.move_to(np.array([6, -3, 0]))
        
        # 创建文字元素
        deep_question_text = Text("深度拷问", font="Microsoft YaHei", font_size=24, weight=BOLD, color=BLUE_E)
        deep_question_text.move_to(np.array([0, -2, 0]))
        deep_question_text.set_opacity(0)
        
        qr_text = Text("扫描二维码关注最新进展", font="SimHei", font_size=18, color=BLACK)
        qr_text.move_to(np.array([0, -3.5, 0]))
        qr_text.set_opacity(0)
        
        # 动画：监督象征缩小移至右下角，深度拷问文字溶解效果
        self.play(
            FadeIn(small_supervision),
            run_time=1
        )
        
        # 深度拷问文字溶解效果
        self.play(
            FadeIn(deep_question_text),
            run_time=1
        )
        
        # 添加光晕效果
        glow = deep_question_text.copy().set_fill(BLUE_E, opacity=0.3).set_stroke(BLUE_E, width=10, opacity=0.3)
        self.play(
            FadeIn(glow),
            run_time=0.5
        )
        
        self.play(
            FadeOut(glow),
            run_time=0.5
        )
        
        # 最后3秒显示二维码引导文字
        self.wait(1)
        
        self.play(
            FadeIn(qr_text),
            run_time=1
        )
        
        # 最后一秒整体淡出
        self.wait(1)
        
        self.play(
            *[FadeOut(mob) for mob in self.mobjects],
            run_time=1
        )